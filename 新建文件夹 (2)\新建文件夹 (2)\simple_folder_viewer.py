import sys
import os
import subprocess
import wave
import struct
import warnings
import traceback
import logging
from datetime import datetime, timedelta
import threading
import time
import concurrent.futures
import socket

# 配置日志系统
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("folder_viewer_log.txt", mode='w')
    ]
)
logger = logging.getLogger("FolderViewer")

# 全局异常处理函数
def log_uncaught_exceptions(ex_type, ex_value, ex_traceback):
    logger.critical("Uncaught exception", exc_info=(ex_type, ex_value, ex_traceback))
    # 确保错误消息在控制台可见
    print(f"严重错误: {ex_value}")
    print("".join(traceback.format_exception(ex_type, ex_value, ex_traceback)))

# 设置全局异常处理器
sys.excepthook = log_uncaught_exceptions

try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QFrame, 
                              QHBoxLayout, QSplitter, QToolBar, QAction, QFileDialog, QMessageBox,
                              QDialog, QLineEdit, QFormLayout, QPushButton, QCheckBox, QComboBox, QProgressBar,
                              QTreeWidget, QTreeWidgetItem, QStyle, QFileIconProvider, QGroupBox, QGridLayout, QSpacerItem,
                              QScrollArea, QHeaderView, QGraphicsDropShadowEffect, QToolButton, QSizePolicy, QButtonGroup,
                              QRadioButton)
    from PyQt5.QtCore import Qt, QRect, QTimer, QSettings, QSize, QFileInfo, QThread, QPointF, QPoint, QEvent
    from PyQt5.QAxContainer import QAxWidget
    from PyQt5.QtGui import QColor, QPainter, QPen, QFont, QIcon, QPixmap, QImage, QRadialGradient, QLinearGradient, QPainterPath, QBrush
    
    logger.info("PyQt5 模块导入成功")
except Exception as e:
    logger.critical(f"PyQt5 模块导入失败: {str(e)}")
    print(f"致命错误: 无法导入PyQt5模块 - {str(e)}")
    sys.exit(1)

try:
    import win32com.client
    import pythoncom
    import win32gui
    import win32ui
    import win32con
    import win32api
    import json
    
    logger.info("Win32 模块导入成功")
except Exception as e:
    logger.critical(f"Win32 模块导入失败: {str(e)}")
    print(f"致命错误: 无法导入Win32模块 - {str(e)}")
    sys.exit(1)

# 过滤掉PyQt5的废弃警告
warnings.filterwarnings("ignore", category=DeprecationWarning)

# 定义全局变量
VIDEO_EXTENSIONS = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.mpg', '.mpeg', '.3gp', '.ts', '.mts', '.m2ts'}

# 全局变量
app = None
COM_INITIALIZED = False

def initialize_qt():
    """初始化Qt环境"""
    global app
    
    # 设置高DPI支持（必须在创建QApplication之前）
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        logger.info("启用高DPI缩放")
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        logger.info("启用高DPI图像")
    
    # 创建应用程序实例
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # 设置默认字体
    default_font = QFont("Microsoft YaHei UI", 9)
    default_font.setHintingPreference(QFont.PreferFullHinting)
    default_font.setPixelSize(12)
    app.setFont(default_font)
    
    return app

def is_video_file(file_path):
    """检查是否为视频文件"""
    ext = os.path.splitext(file_path)[1].lower()
    result = ext in VIDEO_EXTENSIONS
    print(f"检查文件是否为视频: {file_path} -> {result} (扩展名: {ext})")
    return result

def get_video_info(file_path):
    """获取视频文件信息
    返回: (duration_str, size_str)
    """
    try:
        # 获取文件大小
        file_size = os.path.getsize(file_path)
        size_str = format_size(file_size)
        
        # 获取程序所在目录
        app_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        ffprobe_path = os.path.join(app_dir, 'ffprobe.exe')
        
        # 使用ffprobe获取视频信息，使用二进制模式
        cmd = [
            ffprobe_path,
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            file_path
        ]
        
        # 使用subprocess.PIPE并以二进制模式运行
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        
        # 读取输出并解码
        stdout, stderr = process.communicate()
        
        if process.returncode == 0 and stdout:
            try:
                # 解析JSON输出
                probe_data = json.loads(stdout.decode('utf-8', errors='ignore'))
                if 'format' in probe_data and 'duration' in probe_data['format']:
                    duration = float(probe_data['format']['duration'])
                    hours = int(duration // 3600)
                    minutes = int((duration % 3600) // 60)
                    seconds = int(duration % 60)
                    
                    if hours > 0:
                        duration_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                    else:
                        duration_str = f"{minutes:02d}:{seconds:02d}"
                
                    print(f"成功获取视频信息 - 时长: {duration_str}, 大小: {size_str}")
                    return duration_str, size_str
            except json.JSONDecodeError as je:
                print(f"JSON解析失败: {je}")
                return "未知", size_str
            
        print(f"ffprobe 执行失败或无输出: {stderr.decode('utf-8', errors='ignore') if stderr else 'No output'}")
        return "未知", size_str
            
    except Exception as e:
        print(f"获取视频信息出错 ({file_path}): {str(e)}")
        return "未知", "未知"

def format_size(size):
    """格式化文件大小，保持精确的显示"""
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    size_float = float(size)
    unit_index = 0
    
    while size_float >= 1024.0 and unit_index < len(units) - 1:
        size_float /= 1024.0
        unit_index += 1
    
    # 根据大小范围调整小数位数
    if unit_index == 0:  # Bytes
        return f"{int(size_float)} {units[unit_index]}"
    elif unit_index == 1:  # KB
        return f"{size_float:.1f} {units[unit_index]}"
    else:  # MB, GB, TB
        # 如果小数部分很小（小于0.01），就显示2位小数
        if size_float - int(size_float) < 0.01:
            return f"{size_float:.1f} {units[unit_index]}"
        else:
            return f"{size_float:.2f} {units[unit_index]}"

def create_ax_widget_safely(parent=None):
    """
    安全地创建QAxWidget实例
    使用独立的进程防止阻塞
    """
    logger.info("开始创建QAxWidget...")
    
    # 创建信号量用于同步
    creation_done = threading.Event()
    result = {"widget": None, "error": None}
    
    def create_widget_thread():
        try:
            # 确保在新线程中初始化COM
            pythoncom.CoInitialize()
            try:
                # 创建并返回QAxWidget
                widget = QAxWidget(parent)
                logger.info("QAxWidget创建成功")
                result["widget"] = widget
            except Exception as e:
                logger.error(f"创建QAxWidget失败: {str(e)}")
                result["error"] = str(e)
            finally:
                # 清理COM
                pythoncom.CoUninitialize()
        except Exception as e:
            logger.error(f"线程内COM初始化失败: {str(e)}")
            result["error"] = str(e)
        finally:
            # 设置信号，表示创建完成
            creation_done.set()
    
    # 创建并启动线程
    thread = threading.Thread(target=create_widget_thread)
    thread.daemon = True
    thread.start()
    
    # 等待创建完成，最多10秒
    if not creation_done.wait(10.0):
        logger.error("创建QAxWidget超时")
        return None, "创建QAxWidget超时"
    
    return result["widget"], result["error"]

class CustomExplorerWidget(QAxWidget):
    def __init__(self, parent=None):
        # 确保COM环境正确初始化
        global COM_INITIALIZED
        
        try:
            logger.info("初始化CustomExplorerWidget...")
            
            # 不使用直接继承的方式创建QAxWidget
            # 而是先创建基类并设置父对象
            super(QAxWidget, self).__init__(parent)
            self.parent_viewer = parent
            
            # 等待一下确保窗口创建完成
            QApplication.processEvents()
            time.sleep(0.2)
            
            # 使用独立线程创建并初始化ActiveX控件
            self.control_initialized = False
            
            # 使用 WebBrowser 控件而不是 Shell.Explorer
            try:
                logger.info("设置控件类型...")
                self.setControl("{8856F961-340A-11D0-A96B-00C04FD705A2}")
                logger.info("控件类型设置成功")
                
                # 完全禁用拖放功能
                self.setProperty("DragDrop", False)
                self.setAcceptDrops(False)
            except Exception as e:
                logger.error(f"设置控件失败: {str(e)}")
                raise
            
            # 创建样式刷新定时器
            self.style_timer = QTimer(self)
            self.style_timer.timeout.connect(self.apply_style)
            
            # 设置COM超时
            self.shell = None
            self.control = None
            
            logger.info("初始化COM对象...")
            # 使用线程执行COM初始化，避免阻塞UI
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(self._initialize_com_objects)
                try:
                    self.shell, self.control = future.result(timeout=5.0)  # 设置5秒超时
                    if self.shell:
                        logger.info("COM对象初始化成功")
                    else:
                        logger.warning("COM对象初始化部分成功或失败")
                except (concurrent.futures.TimeoutError, Exception) as e:
                    logger.error(f"COM初始化超时或出错: {str(e)}")
                    # 如果初始化失败，标记为失败但不终止程序
                    self.shell = None
                    self.control = None
            
            # 应用样式
            self.apply_style()
            logger.info("应用样式成功")
            
            # 使用 Windows API 修改窗口样式
            try:
                hwnd = self.winId()
                logger.info(f"获取窗口句柄: {hwnd}")
                
                # 获取当前窗口样式
                style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
                ex_style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
                
                # 移除所有边框和标题栏相关的样式
                style &= ~(win32con.WS_CAPTION | 
                          win32con.WS_THICKFRAME | 
                          win32con.WS_MINIMIZE | 
                          win32con.WS_MAXIMIZE | 
                          win32con.WS_SYSMENU | 
                          win32con.WS_DLGFRAME |
                          win32con.WS_BORDER |
                          win32con.WS_EX_CLIENTEDGE |
                          win32con.WS_EX_WINDOWEDGE |
                          win32con.WS_EX_DLGMODALFRAME |
                          win32con.WS_OVERLAPPED)
                
                # 移除扩展样式
                ex_style &= ~(win32con.WS_EX_CLIENTEDGE | 
                             win32con.WS_EX_WINDOWEDGE |
                             win32con.WS_EX_DLGMODALFRAME |
                             win32con.WS_EX_APPWINDOW |
                             win32con.WS_EX_TOOLWINDOW |
                             win32con.WS_EX_LAYERED)
                
                # 添加无边框样式
                style |= win32con.WS_POPUP
                ex_style |= win32con.WS_EX_CONTROLPARENT
                
                # 应用新样式
                win32gui.SetWindowLong(hwnd, win32con.GWL_STYLE, style)
                win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, ex_style)
                
                # 强制重绘
                win32gui.SetWindowPos(hwnd, None, 0, 0, 0, 0,
                    win32con.SWP_FRAMECHANGED | win32con.SWP_NOMOVE |
                    win32con.SWP_NOSIZE | win32con.SWP_NOZORDER |
                    win32con.SWP_NOOWNERZORDER)
                
                logger.info("窗口样式设置成功")
            except Exception as e:
                logger.error(f"设置窗口样式出错: {str(e)}")
                
        except Exception as e:
            logger.critical(f"CustomExplorerWidget 初始化出错: {str(e)}", exc_info=True)
            raise
    
    def _initialize_com_objects(self):
        """在单独线程中初始化COM对象，避免阻塞UI"""
        try:
            # 确保当前线程已初始化COM
            pythoncom.CoInitialize()
            
            # 初始化Shell对象
            shell = win32com.client.Dispatch("Shell.Application")
            
            # 初始化控件COM接口
            try:
                control = win32com.client.Dispatch(pythoncom.CoCreateInstance(
                    "{8856F961-340A-11D0-A96B-00C04FD705A2}",
                    None,
                    pythoncom.CLSCTX_SERVER,
                    pythoncom.IID_IDispatch
                ))
                
                # 设置浏览器特定属性
                control.Silent = True
                control.MenuBar = False
                control.StatusBar = False
                control.ToolBar = False
                control.FullScreen = True
            except Exception as e:
                print(f"创建控件COM接口失败: {str(e)}")
                control = None
            
            return shell, control
        except Exception as e:
            print(f"COM对象初始化失败: {str(e)}")
            return None, None
        finally:
            # 清理当前线程的COM
            pythoncom.CoUninitialize()
            
    def apply_style(self):
        """应用控件样式"""
        try:
            # 设置控件自身的样式 - 使用更强的立体效果
            self.setStyleSheet("""
                QAxWidget {
                    border: 1px solid #bdc3c7;
                    border-radius: 5px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ffffff,
                        stop:0.4 #f8f9fa,
                        stop:0.8 #e9ecef,
                        stop:1 #dee2e6) !important;
                    box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.2),
                               inset 1px 1px 2px rgba(255, 255, 255, 0.8),
                               inset -1px -1px 2px rgba(0, 0, 0, 0.1);
                }
                QAxWidget:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ffffff,
                        stop:0.4 #ffffff,
                        stop:0.8 #f8f9fa,
                        stop:1 #e9ecef) !important;
                }
            """)
            
            # 尝试通过COM接口设置背景颜色
            try:
                # 尝试使用COM对象的方法设置背景颜色
                doc = self.dynamicCall("Document()")
                if doc:
                    body = doc.documentElement.getElementsByTagName("body")[0]
                    if body:
                        body.style.backgroundColor = "#ffffff"
                        body.style.boxShadow = "inset 1px 1px 3px rgba(0,0,0,0.1)"
            except:
                pass

            # 使用Windows API设置背景颜色
            try:
                hwnd = self.winId()
                bg_brush = win32gui.CreateSolidBrush(win32api.RGB(255, 255, 255))  # #ffffff
                win32gui.SetClassLong(hwnd, win32con.GCL_HBRBACKGROUND, bg_brush)
                win32gui.InvalidateRect(hwnd, None, True)
            except:
                pass
            
        except Exception as e:
            print(f"应用样式出错: {str(e)}")
            
    def navigate_to(self, path):
        """导航到指定路径"""
        try:
            # 使用特殊的标志来禁用导航UI
            SBSP_DEFMODE = 0x0000
            SBSP_NONAVIGATE = 0x1000
            SBSP_SAMEBROWSER = 0x0001
            SBSP_ABSOLUTE = 0x0000
            SBSP_DEFBROWSER = 0x0000
            SBSP_OPENMODE = 0x0010
            SBSP_NOTRANSFERHIST = 0x0080
            
            # 组合标志
            flags = (SBSP_DEFMODE | SBSP_NONAVIGATE | SBSP_SAMEBROWSER | 
                    SBSP_ABSOLUTE | SBSP_DEFBROWSER | SBSP_OPENMODE | 
                    SBSP_NOTRANSFERHIST)
            
            # 导航到目标路径
            self.dynamicCall("Navigate2(const QString&, uint)", path, flags)
            
            # 在导航后再次禁用导航栏
            self.dynamicCall("PutProperty(const QString&, const QVariant&)", "NoLeftNavPane", True)
            self.dynamicCall("PutProperty(const QString&, const QVariant&)", "NoNavigation", True)
            self.dynamicCall("PutProperty(const QString&, const QVariant&)", "NoTreeView", True)
            self.dynamicCall("PutProperty(const QString&, const QVariant&)", "NoNavBar", True)
            
            # 导航后重新应用样式
            self.apply_style()
            
            # 启动定时器，持续应用样式一段时间
            self.style_timer.start(100)  # 每100ms应用一次
            QTimer.singleShot(1000, self.style_timer.stop)  # 1秒后停止
            
            # 添加延迟重复应用样式，确保样式不被覆盖
            QTimer.singleShot(200, self.apply_style)
            QTimer.singleShot(500, self.apply_style)
            QTimer.singleShot(800, self.apply_style)
            QTimer.singleShot(1200, self.apply_style)
            QTimer.singleShot(2000, self.apply_style)
            
            # 尝试使用 Shell.Application 接口
            try:
                if self.shell:
                    folder = self.shell.NameSpace(path)
                    if folder:
                        folder.NavigationStyle = 0  # 禁用导航
            except Exception as e:
                print(f"设置导航样式出错: {str(e)}")
            
        except Exception as e:
            print(f"导航出错: {str(e)}")
            
    def deleteLater(self):
        """删除控件时清理资源"""
        try:
            # 停止计时器
            if hasattr(self, 'style_timer') and self.style_timer:
                self.style_timer.stop()
                
            # 清空引用
            self.shell = None
            self.control = None
        except:
            pass
            
        # 调用父类方法
        super().deleteLater()

class DropArea(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.setAcceptDrops(True)
        self.setMinimumWidth(200)
        
        # 创建主布局
        self.layout = QVBoxLayout(self)
        self.layout.setSpacing(12)
        self.layout.setContentsMargins(12, 12, 12, 12)
        
        # 创建提示标签
        self.label = QLabel("拖放文件夹\n到此处查看")
        self.label.setAlignment(Qt.AlignCenter)
        self.label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei UI';
                font-size: 15px;
                font-weight: 500;
                color: #1a237e;
                background: transparent;
                padding: 24px;
            }
        """)
        self.layout.addWidget(self.label)
        
        # 创建文件夹列表和工具栏容器
        list_container = QWidget()
        list_layout = QVBoxLayout(list_container)
        list_layout.setContentsMargins(0, 0, 0, 0)
        list_layout.setSpacing(8)
        
        # 创建工具栏
        toolbar = QWidget()
        toolbar_layout = QHBoxLayout(toolbar)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)
        toolbar_layout.setSpacing(8)
        
        # 创建批量截图按钮
        self.batch_screenshot_button = QPushButton("批量截图")
        self.batch_screenshot_button.setStyleSheet("""
            QPushButton {
                font-family: 'Microsoft YaHei UI';
                font-size: 13px;
                font-weight: 500;
                padding: 8px 16px;
                background: #e8eaf6;
                border: none;
                border-radius: 6px;
                color: #1a237e;
            }
            QPushButton:hover {
                background: #c5cae9;
            }
            QPushButton:pressed {
                background: #9fa8da;
                color: #1a237e;
            }
        """)
        self.batch_screenshot_button.clicked.connect(self.batch_screenshot)
        toolbar_layout.addWidget(self.batch_screenshot_button)
        
        # 创建清空按钮
        self.clear_button = QPushButton("清空列表")
        self.clear_button.setStyleSheet("""
            QPushButton {
                font-family: 'Microsoft YaHei UI';
                font-size: 13px;
                font-weight: 500;
                padding: 8px 16px;
                background: #fef2f2;
                border: none;
                border-radius: 6px;
                color: #dc2626;
            }
            QPushButton:hover {
                background: #fee2e2;
            }
            QPushButton:pressed {
                background: #fecaca;
                color: #b91c1c;
            }
        """)
        self.clear_button.clicked.connect(self.clear_folders)
        toolbar_layout.addWidget(self.clear_button)
        toolbar_layout.addStretch()
        
        list_layout.addWidget(toolbar)
        
        # 创建文件夹列表
        self.folders_tree = QTreeWidget(self)
        self.folders_tree.setHeaderLabels(["文件夹"])
        self.folders_tree.setStyleSheet("""
            QTreeWidget {
                font-family: 'Microsoft YaHei UI';
                font-size: 13px;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background: white;
                padding: 8px;
            }
            QTreeWidget::item {
                padding: 6px;
                border-radius: 4px;
            }
            QTreeWidget::item:hover {
                background: #f8fafc;
            }
            QTreeWidget::item:selected {
                background: #e8eaf6;
                color: #1a237e;
                border: 1px solid #c5cae9;
            }
            QTreeWidget QHeaderView::section {
                font-weight: 600;
                color: #1a237e;
                background: #f8fafc;
                border: none;
                border-bottom: 1px solid #e2e8f0;
                padding: 8px;
            }
            QScrollBar:vertical {
                border: none;
                background: #f1f5f9;
                width: 10px;
                border-radius: 5px;
            }
            QScrollBar::handle:vertical {
                background: #c5cae9;
                min-height: 30px;
                border-radius: 5px;
            }
            QScrollBar::handle:vertical:hover {
                background: #7986cb;
            }
            QScrollBar::add-line:vertical,
            QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical,
            QScrollBar::sub-page:vertical {
                background: none;
            }
        """)
        self.folders_tree.itemClicked.connect(self.on_folder_clicked)
        list_layout.addWidget(self.folders_tree)
        
        self.layout.addWidget(list_container)
        
        # 设置样式
        self.setStyleSheet("""
            DropArea {
                background: #f8f9fa;
                border: 2px dashed #c5cae9;
                border-radius: 12px;
            }
            DropArea:hover {
                background: #f1f5f9;
                border-color: #1a237e;
            }
        """)
        
        # 存储已添加的文件夹路径
        self.folder_paths = set()
    
    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            self.setStyleSheet("""
                DropArea {
                    background: #e8eaf6;
                    border: 2px dashed #1a237e;
                    border-radius: 12px;
                }
            """)
            event.acceptProposedAction()
    
    def dragLeaveEvent(self, event):
        self.setStyleSheet("""
            DropArea {
                background: #ffffff;
                border: 2px dashed #c5cae9;
                border-radius: 12px;
            }
        """)
    
    def dropEvent(self, event):
        urls = event.mimeData().urls()
        try:
            for url in urls:
                path = url.toLocalFile()
                if os.path.isdir(path):
                    # 添加到列表中
                    self.add_folder_to_list(path)
                    
                    # 如果是第一个或者当前选中的文件夹，则打开它
                    if self.folders_tree.topLevelItemCount() == 1 or (self.folders_tree.currentItem() and self.folders_tree.currentItem().toolTip(0) == path):
                        if hasattr(self.main_window, 'handle_folder_drop'):
                            self.main_window.handle_folder_drop(path)
            
            # 手动触发一次重绘和布局更新
            self.folders_tree.repaint()
            self.folders_tree.updateGeometry()
            self.update()
            
            # 处理所有挂起的事件
            QApplication.processEvents()
            
            # 尝试调整父窗口大小以触发布局刷新
            if self.main_window:
                current_size = self.main_window.size()
                self.main_window.resize(current_size.width() + 1, current_size.height())
                QApplication.processEvents()
                self.main_window.resize(current_size)
                QApplication.processEvents()
                
        except Exception as e:
            print(f"拖放文件夹错误: {str(e)}")
        
        self.setStyleSheet("""
            DropArea {
                background: #f8f9fa;
                border: 2px dashed #c5cae9;
                border-radius: 12px;
            }
            DropArea:hover {
                background: #f1f5f9;
                border-color: #1a237e;
            }
        """)
        event.acceptProposedAction()
    
    def add_folder_to_list(self, folder_path):
        """添加文件夹到列表，如果已存在则不添加但会选择它"""
        try:
            # 先检查路径是否有效
            if not os.path.isdir(folder_path):
                print(f"无效的文件夹路径: {folder_path}")
                return
                
            # 检查文件夹是否已在列表中
            if folder_path in self.folder_paths:
                # 找到并选中现有项
                for i in range(self.folders_tree.topLevelItemCount()):
                    item = self.folders_tree.topLevelItem(i)
                    if item and item.toolTip(0) == folder_path:
                        self.folders_tree.setCurrentItem(item)
                        break
                return
                
            # 添加新项到列表
            self.folder_paths.add(folder_path)
            item = QTreeWidgetItem(self.folders_tree)
            item.setText(0, os.path.basename(folder_path))
            item.setToolTip(0, folder_path)
            
            # 设置文件夹图标
            try:
                icon_provider = QFileIconProvider()
                icon = icon_provider.icon(QFileInfo(folder_path))
                item.setIcon(0, icon)
            except Exception as icon_error:
                print(f"设置文件夹图标错误: {str(icon_error)}")
                # 使用默认图标
                pass
            
            # 选中新添加的项
            self.folders_tree.setCurrentItem(item)
            
            # 强制更新树控件显示
            self.folders_tree.repaint()
            self.folders_tree.viewport().update()
            self.folders_tree.updateGeometry()
            self.update()
            
            # 强制调整父窗口的布局
            if self.main_window:
                # 刷新分隔器
                if hasattr(self.main_window, 'splitter'):
                    self.main_window.splitter.update()
                
                # 刷新主窗口布局
                self.main_window.updateGeometry()
                QApplication.processEvents()
                
                # 尝试调整主窗口大小，触发布局重新计算
                current_size = self.main_window.size()
                self.main_window.resize(current_size.width() + 1, current_size.height())
                QApplication.processEvents()
                self.main_window.resize(current_size)
                QApplication.processEvents()
            
            print(f"成功添加文件夹到列表: {folder_path}")
        except Exception as e:
            print(f"添加文件夹到列表错误: {str(e)}")
            
            # 尝试更安全的方式添加
            try:
                if folder_path not in self.folder_paths and os.path.isdir(folder_path):
                    self.folder_paths.add(folder_path)
                    item = QTreeWidgetItem()
                    item.setText(0, os.path.basename(folder_path))
                    item.setToolTip(0, folder_path)
                    self.folders_tree.addTopLevelItem(item)
                    self.folders_tree.setCurrentItem(item)
                    self.folders_tree.repaint()
                    print(f"使用备用方法添加文件夹: {folder_path}")
                    
                    # 尝试再次强制更新
                    QApplication.processEvents()
                    self.update()
            except Exception as backup_error:
                print(f"备用添加方法也失败: {str(backup_error)}")
    
    def on_folder_clicked(self, item):
        folder_path = item.toolTip(0)
        if hasattr(self.main_window, 'handle_folder_drop'):
            self.main_window.handle_folder_drop(folder_path)
    
    def clear_folders(self):
        """清空文件夹列表"""
        try:
            # 清空树控件
            self.folders_tree.clear()
            # 清空路径集合
            self.folder_paths.clear()
            
            # 强制更新树控件显示
            self.folders_tree.repaint()
            self.folders_tree.viewport().update()
            self.folders_tree.updateGeometry()
            self.update()
            
            # 通知主窗口重置状态
            if hasattr(self.main_window, 'current_folder') and self.main_window.current_folder:
                # 重置主窗口的状态
                if self.main_window.explorer:
                    self.main_window.explorer_layout.removeWidget(self.main_window.explorer)
                    self.main_window.explorer.deleteLater()
                    self.main_window.explorer = None
                
                # 显示提示标签
                if hasattr(self.main_window, 'label'):
                    self.main_window.label.setText('将文件夹拖放到右侧区域查看')
                    self.main_window.label.show()
                
                # 重置当前文件夹
                self.main_window.current_folder = None
                self.main_window.setWindowTitle('文件夹查看器')
                
                # 强制更新布局
                self.main_window.explorer_layout.update()
                self.main_window.central_widget.updateGeometry()
                self.main_window.update()
                
                # 刷新分隔器
                if hasattr(self.main_window, 'splitter'):
                    self.main_window.splitter.update()
                
                # 处理所有挂起的事件
                QApplication.processEvents()
                
                # 尝试调整主窗口大小，触发布局重新计算
                current_size = self.main_window.size()
                self.main_window.resize(current_size.width() + 1, current_size.height())
                QApplication.processEvents()
                self.main_window.resize(current_size)
                QApplication.processEvents()
                
        except Exception as e:
            print(f"清空文件夹列表错误: {str(e)}")
            # 如果发生错误，尝试强制清空
            try:
                while self.folders_tree.topLevelItemCount() > 0:
                    self.folders_tree.takeTopLevelItem(0)
                self.folder_paths = set()
                
                # 强制更新
                self.folders_tree.repaint()
                self.update()
                QApplication.processEvents()
            except Exception as backup_error:
                print(f"备用清空方法也失败: {str(backup_error)}")
                pass
    
    def batch_screenshot(self):
        """批量截图所有文件夹"""
        try:
            # 检查是否有文件夹
            if not self.folders_tree.topLevelItemCount():
                QMessageBox.information(self, "提示", "请先添加文件夹到列表中")
                return
            
            # 获取当前时间戳作为文件名的一部分
            current_time = datetime.now()
            date_time = current_time.strftime('%Y%m%d_%H%M%S')
            timestamp = str(int(current_time.timestamp()))
            
            # 保存当前状态
            current_folder = None
            compress_dialog_open = False
            if hasattr(self.main_window, 'current_folder'):
                current_folder = self.main_window.current_folder
            if hasattr(self.main_window, 'compress_dialog') and self.main_window.compress_dialog:
                compress_dialog_open = self.main_window.compress_dialog.isVisible()
            
            # 处理每个文件夹
            success_count = 0
            for i in range(self.folders_tree.topLevelItemCount()):
                try:
                    # 获取文件夹路径
                    item = self.folders_tree.topLevelItem(i)
                    folder_path = item.toolTip(0)
                    if not os.path.isdir(folder_path):
                        continue
                        
                    folder_name = os.path.basename(folder_path)
                    
                    # 先关闭任何存在的压缩对话框，确保完全重置
                    if hasattr(self.main_window, 'compress_dialog') and self.main_window.compress_dialog:
                        self.main_window.compress_dialog.close()
                        self.main_window.compress_dialog.deleteLater()
                        self.main_window.compress_dialog = None
                        QApplication.processEvents()
                        # 增加短暂延时确保对话框完全关闭
                        time.sleep(0.2)
                    
                    # 打开文件夹
                    if hasattr(self.main_window, 'handle_folder_drop'):
                        self.main_window.handle_folder_drop(folder_path)
                        
                        # 等待文件夹加载完成
                        QApplication.processEvents()
                        time.sleep(0.3)  # 增加等待时间确保加载完成
                        
                        # 重新创建压缩对话框
                        if hasattr(self.main_window, 'compress_folder'):
                            self.main_window.compress_folder()
                        
                        # 等待压缩对话框更新完成
                        QApplication.processEvents()
                        time.sleep(0.5)  # 增加等待时间确保对话框完全加载
                        
                        # 强制再次更新视频显示，确保显示正确
                        if hasattr(self.main_window, 'compress_dialog') and self.main_window.compress_dialog:
                            # 先尝试强制清空视频布局
                            if hasattr(self.main_window.compress_dialog, 'videos_layout'):
                                while self.main_window.compress_dialog.videos_layout.count():
                                    item = self.main_window.compress_dialog.videos_layout.takeAt(0)
                                    if item.widget():
                                        item.widget().deleteLater()
                                QApplication.processEvents()
                            
                            # 确保文件夹路径设置正确
                            self.main_window.compress_dialog.current_folder = folder_path
                            
                            # 更新文件列表和视频显示
                            self.main_window.compress_dialog.update_files_list(folder_path)
                            QApplication.processEvents()
                            time.sleep(0.2)
                            
                            # 视频显示功能已移除
                            QApplication.processEvents()
                            time.sleep(0.2)
                            
                            # 强制重绘对话框
                            self.main_window.compress_dialog.repaint()
                            QApplication.processEvents()
                            time.sleep(0.2)
                        
                        # 生成截图文件名
                        screenshot_name = f"{folder_name}_{date_time}_{timestamp}.png"
                        screenshot_path = os.path.join(folder_path, screenshot_name)
                        
                        # 截图前再次确保UI已完全更新
                        QApplication.processEvents()
                        time.sleep(0.3)
                        
                        # 截图
                        self.main_window.take_screenshot(True, screenshot_path)
                        
                        # 等待截图完成
                        QApplication.processEvents()
                        time.sleep(0.2)
                        
                        # 关闭当前的压缩对话框，为下一个文件夹准备
                        if hasattr(self.main_window, 'compress_dialog') and self.main_window.compress_dialog:
                            self.main_window.compress_dialog.close()
                            self.main_window.compress_dialog.deleteLater()
                            self.main_window.compress_dialog = None
                            QApplication.processEvents()
                            time.sleep(0.2)  # 等待对话框完全关闭
                        
                        success_count += 1
                except Exception as e:
                    print(f"处理文件夹 {folder_path} 时出错: {str(e)}")
                    continue
            
            # 恢复原始状态
            if current_folder and hasattr(self.main_window, 'handle_folder_drop'):
                # 先关闭任何存在的压缩对话框
                if hasattr(self.main_window, 'compress_dialog') and self.main_window.compress_dialog:
                    self.main_window.compress_dialog.close()
                    self.main_window.compress_dialog.deleteLater()
                    self.main_window.compress_dialog = None
                    QApplication.processEvents()
                    time.sleep(0.2)
                
                # 重新打开原来的文件夹
                self.main_window.handle_folder_drop(current_folder)
                
                # 如果之前有打开压缩对话框，重新打开它
                if compress_dialog_open and hasattr(self.main_window, 'compress_folder'):
                    # 确保先关闭任何可能存在的压缩对话框
                    if hasattr(self.main_window, 'compress_dialog') and self.main_window.compress_dialog:
                        self.main_window.compress_dialog.close()
                        self.main_window.compress_dialog.deleteLater()
                        self.main_window.compress_dialog = None
                        QApplication.processEvents()
                        time.sleep(0.2)
                    
                    # 重新打开压缩对话框
                    QTimer.singleShot(300, self.main_window.compress_folder)
            
            # 显示完成消息
            if success_count > 0:
                QMessageBox.information(self, "完成", f"已完成 {success_count} 个文件夹的截图")
            else:
                QMessageBox.warning(self, "警告", "没有成功完成任何文件夹的截图")
                
        except Exception as e:
            QMessageBox.warning(self, "错误", f"批量截图过程中出错: {str(e)}")
            print(f"批量截图错误: {str(e)}")

class CompressDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)

        # 设置窗口属性 - 启用透明背景以支持现代化设计
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint | Qt.WindowSystemMenuHint)

        # 基本设置 - 调整为更现代的尺寸
        self.setFixedWidth(1100)  # 增加宽度以适应新设计
        self.setMinimumHeight(600)
        self.setMaximumHeight(750)
        self.parent_window = parent
        self.current_folder = None

        # 初始化拖动相关的属性
        self._drag_pos = None

        # 创建文件图标提供者
        self.icon_provider = QFileIconProvider()

        # 启用拖放功能
        self.setAcceptDrops(True)

        # 定义现代化的复选框样式
        self.checkbox_style = """
            QCheckBox {
                font-size: 13px;
                color: #2d3748;
                background: transparent;
                font-weight: 500;
                padding: 4px 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff,
                    stop:1 #f7fafc);
            }
            QCheckBox::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea,
                    stop:1 #764ba2);
                border-color: #667eea;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTEuNSAzLjVMNS41IDkuNUwyLjUgNi41IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+);
            }
            QCheckBox::indicator:hover {
                border-color: #a78bfa;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff,
                    stop:1 #faf5ff);
            }
            QCheckBox:hover {
                color: #553c9a;
            }
        """

        # 创建主容器 - 带有玻璃态效果
        self.main_container = QWidget()
        self.main_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.3 rgba(248, 250, 252, 0.98),
                    stop:0.7 rgba(241, 245, 249, 0.95),
                    stop:1 rgba(255, 255, 255, 0.92));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 20px;
                backdrop-filter: blur(20px);
            }
        """)

        # 添加主容器阴影
        container_shadow = QGraphicsDropShadowEffect()
        container_shadow.setBlurRadius(40)
        container_shadow.setColor(QColor(0, 0, 0, 30))
        container_shadow.setOffset(0, 10)
        self.main_container.setGraphicsEffect(container_shadow)

        # 创建主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(20, 20, 20, 20)
        self.main_layout.setSpacing(0)
        self.main_layout.addWidget(self.main_container)

        # 创建容器内部布局
        self.container_layout = QVBoxLayout(self.main_container)
        self.container_layout.setContentsMargins(0, 0, 0, 0)
        self.container_layout.setSpacing(0)

        # 创建现代化标题栏
        self.header = self.create_modern_header()
        self.container_layout.addWidget(self.header)

        # 初始化所有UI组件
        self.init_ui_components()

        # 设置窗口位置
        self.restore_window_position()

        # 初始化定时器
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.auto_refresh_files_list)
        self.refresh_timer.setInterval(1000)
        self.last_file_states = {}

        # 默认不启用自动刷新
        self.auto_refresh.setChecked(False)

        # 设置全局样式
        self.setStyleSheet("""
            CompressDialog {
                background: transparent;
            }
            * {
                font-family: 'Segoe UI', 'Microsoft YaHei UI', sans-serif;
            }
        """)
        
    def create_modern_header(self):
        """创建现代化玻璃态标题栏"""
        header = QWidget()
        header.setFixedHeight(60)
        header.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(102, 126, 234, 0.1),
                    stop:0.5 rgba(118, 75, 162, 0.08),
                    stop:1 rgba(102, 126, 234, 0.1));
                border: none;
                border-top-left-radius: 20px;
                border-top-right-radius: 20px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)

        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(24, 0, 24, 0)
        header_layout.setSpacing(16)

        # 添加装饰性图标
        icon_container = QWidget()
        icon_container.setFixedSize(40, 40)
        icon_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea,
                    stop:1 #764ba2);
                border-radius: 20px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)

        # 添加图标阴影
        icon_shadow = QGraphicsDropShadowEffect()
        icon_shadow.setBlurRadius(15)
        icon_shadow.setColor(QColor(102, 126, 234, 80))
        icon_shadow.setOffset(0, 3)
        icon_container.setGraphicsEffect(icon_shadow)

        icon_layout = QHBoxLayout(icon_container)
        icon_layout.setContentsMargins(0, 0, 0, 0)
        icon_layout.setAlignment(Qt.AlignCenter)

        icon_label = QLabel("📦")
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                background: transparent;
                border: none;
            }
        """)
        icon_layout.addWidget(icon_label)

        header_layout.addWidget(icon_container)

        # 标题区域
        title_container = QWidget()
        title_container.setStyleSheet("background: transparent;")
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 8, 0, 8)
        title_layout.setSpacing(2)

        # 主标题
        main_title = QLabel("智能压缩助手")
        main_title.setStyleSheet("""
            QLabel {
                color: #2d3748;
                font-size: 18px;
                font-weight: 700;
                background: transparent;
                border: none;
            }
        """)
        title_layout.addWidget(main_title)

        # 副标题
        sub_title = QLabel("高效压缩 · 智能管理")
        sub_title.setStyleSheet("""
            QLabel {
                color: #718096;
                font-size: 12px;
                font-weight: 400;
                background: transparent;
                border: none;
            }
        """)
        title_layout.addWidget(sub_title)

        header_layout.addWidget(title_container)
        header_layout.addStretch()

        # 右侧按钮区域
        buttons_container = QWidget()
        buttons_container.setStyleSheet("background: transparent;")
        buttons_layout = QHBoxLayout(buttons_container)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(8)

        # 最小化按钮
        minimize_button = QPushButton("−")
        minimize_button.setFixedSize(32, 32)
        minimize_button.setCursor(Qt.PointingHandCursor)
        minimize_button.setStyleSheet("""
            QPushButton {
                font-size: 16px;
                font-weight: bold;
                color: #718096;
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 16px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.2);
                color: #4a5568;
                border-color: rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: rgba(255, 255, 255, 0.15);
                transform: scale(0.95);
            }
        """)
        minimize_button.clicked.connect(self.showMinimized)

        # 关闭按钮
        close_button = QPushButton("×")
        close_button.setFixedSize(32, 32)
        close_button.setCursor(Qt.PointingHandCursor)
        close_button.setStyleSheet("""
            QPushButton {
                font-size: 18px;
                font-weight: bold;
                color: #e53e3e;
                background: rgba(229, 62, 62, 0.1);
                border: 1px solid rgba(229, 62, 62, 0.2);
                border-radius: 16px;
            }
            QPushButton:hover {
                background: rgba(229, 62, 62, 0.2);
                color: #c53030;
                border-color: rgba(229, 62, 62, 0.3);
            }
            QPushButton:pressed {
                background: rgba(229, 62, 62, 0.15);
                transform: scale(0.95);
            }
        """)
        close_button.clicked.connect(self.reject)

        buttons_layout.addWidget(minimize_button)
        buttons_layout.addWidget(close_button)
        header_layout.addWidget(buttons_container)

        return header
        
    # 视频区域功能已移除
    
    # 视频卡片功能已移除
        
    def update_files_list(self, folder_path):
        """更新文件列表"""
        try:
            # 显示等待光标，表示正在处理
            QApplication.setOverrideCursor(Qt.WaitCursor)
            
            self.current_folder = folder_path  # 保存当前文件夹路径
            self.files_tree.clear()
            
            # 视频文件显示功能已移除
            
            total_files = 0
            video_count = 0
            total_size = 0
            total_duration_seconds = 0
            
            # 创建根文件夹项
            root_folder_name = os.path.basename(folder_path)
            root_item = QTreeWidgetItem(self.files_tree)
            root_item.setText(0, root_folder_name)
            root_item.setIcon(0, self.icon_provider.icon(QFileInfo(folder_path)))

            # 设置缩略图大小
            THUMBNAIL_SIZE = 16
            
            for root, dirs, files in os.walk(folder_path):
                print(f"\n处理目录: {root}")
                
                # 创建当前文件夹的树节点
                relative_path = os.path.relpath(root, folder_path)
                if relative_path == '.':
                    parent = root_item
                else:
                    # 查找或创建父目录节点
                    parent_path = os.path.dirname(relative_path)
                    if parent_path:
                        parent_items = self.files_tree.findItems(parent_path, Qt.MatchExactly | Qt.MatchRecursive)
                        if parent_items:
                            parent = parent_items[0]
                        else:
                            parent = QTreeWidgetItem(root_item)
                            parent.setText(0, parent_path)
                            parent.setIcon(0, self.icon_provider.icon(QFileInfo(os.path.join(folder_path, parent_path))))
                    else:
                        parent = root_item
                
                # 添加子文件夹
                for dir_name in dirs:
                    dir_path = os.path.join(root, dir_name)
                    dir_item = QTreeWidgetItem(parent)
                    dir_item.setText(0, dir_name)
                    dir_item.setIcon(0, self.icon_provider.icon(QFileInfo(dir_path)))
                    # 设置其他列为空
                    dir_item.setText(1, "")
                    dir_item.setText(2, "")
                    dir_item.setText(3, "")
                
                # 添加文件
                for file in sorted(files):
                    file_path = os.path.join(root, file)
                    file_item = QTreeWidgetItem(parent)
                    
                    # 设置文件名
                    file_item.setText(0, file)
                    
                    # 检查是否为图片文件
                    if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                        try:
                            # 加载图片并创建缩略图
                            image = QImage(file_path)
                            if not image.isNull():
                                scaled_image = image.scaled(THUMBNAIL_SIZE, THUMBNAIL_SIZE, 
                                                          Qt.KeepAspectRatio, 
                                                          Qt.SmoothTransformation)
                                pixmap = QPixmap.fromImage(scaled_image)
                                file_item.setIcon(0, QIcon(pixmap))
                            else:
                                file_item.setIcon(0, self.icon_provider.icon(QFileInfo(file_path)))
                        except Exception as e:
                            print(f"创建缩略图失败: {str(e)}")
                            file_item.setIcon(0, self.icon_provider.icon(QFileInfo(file_path)))
                    # 检查是否为视频文件
                    elif is_video_file(file_path):
                        # 尝试获取视频缩略图
                        video_icon = get_video_thumbnail(file_path, THUMBNAIL_SIZE)
                        if video_icon:
                            file_item.setIcon(0, video_icon)
                        else:
                            file_item.setIcon(0, self.icon_provider.icon(QFileInfo(file_path)))
                    else:
                        file_item.setIcon(0, self.icon_provider.icon(QFileInfo(file_path)))
                    
                    # 如果是视频文件，获取时长和大小
                    if is_video_file(file_path):
                        print(f"处理视频文件: {file_path}")
                        try:
                            duration_str, size_str = get_video_info(file_path)
                            print(f"获取到视频信息 - 时长: {duration_str}, 大小: {size_str}")
                            
                            video_count += 1
                            
                            # 累计大小
                            size = os.path.getsize(file_path)
                            total_size += size
                            
                            # 累计时长
                            if duration_str != "未知" and ':' in duration_str:
                                time_parts = duration_str.split(':')
                                if len(time_parts) == 3:  # HH:MM:SS
                                    total_duration_seconds += (int(time_parts[0]) * 3600 + 
                                                            int(time_parts[1]) * 60 + 
                                                            int(time_parts[2]))
                                elif len(time_parts) == 2:  # MM:SS
                                    total_duration_seconds += int(time_parts[0]) * 60 + int(time_parts[1])
                            
                            # 设置大小和时长
                            file_item.setText(1, size_str)
                            file_item.setText(2, duration_str)
                            
                            # 设置对齐方式
                            file_item.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
                            file_item.setTextAlignment(2, Qt.AlignRight | Qt.AlignVCenter)
                            
                            # 设置工具提示
                            file_item.setToolTip(1, f"文件大小: {size_str}")
                            file_item.setToolTip(2, f"视频时长: {duration_str}")
                        except Exception as e:
                            print(f"获取视频信息出错: {str(e)}")
                            file_item.setText(1, "未知")
                            file_item.setText(2, "未知")
                    else:
                        # 非视频文件只显示大小
                        try:
                            size = os.path.getsize(file_path)
                            size_str = format_size(size)
                            file_item.setText(1, size_str)
                            file_item.setTextAlignment(1, Qt.AlignRight | Qt.AlignVCenter)
                            file_item.setToolTip(1, f"文件大小: {size_str}")
                            file_item.setText(2, "")  # 非视频文件不显示时长
                        except:
                            file_item.setText(1, "未知")
                            file_item.setText(2, "")
                    
                    # 获取修改时间
                    try:
                        mtime = os.path.getmtime(file_path)
                        mtime_str = datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M')
                        file_item.setText(3, mtime_str)
                        file_item.setTextAlignment(3, Qt.AlignRight | Qt.AlignVCenter)
                        file_item.setToolTip(3, f"修改时间: {mtime_str}")
                    except:
                        file_item.setText(3, "未知")
                    
                    total_files += 1
            
            # 更新视频信息显示
            self.video_count_label.setText(f"视频: {video_count}个")
            self.total_size_label.setText(f"大小: {format_size(total_size)}")
            
            # 格式化总时长
            total_hours = total_duration_seconds // 3600
            total_minutes = (total_duration_seconds % 3600) // 60
            total_seconds = total_duration_seconds % 60
            
            if total_hours > 0:
                duration_text = f"{total_hours}:{total_minutes:02d}:{total_seconds:02d}"
            else:
                duration_text = f"{total_minutes}:{total_seconds:02d}"
            
            self.total_duration_label.setText(duration_text)
            
            # 更新文件计数，显示"共xx个文件"
            self.files_count_label.setText(f"共{total_files}个文件")
            
            # 展开所有节点
            self.files_tree.expandAll()
            
            # 优化列宽分配，文件名列优先，隐藏修改时间列
            min_widths = [0, 70, 60, 0]  # 各列的最小宽度
            
            # 调整大小和时长列（索引1和2）
            for i in [1, 2]:
                self.files_tree.resizeColumnToContents(i)
                width = max(min_widths[i], self.files_tree.columnWidth(i))
                self.files_tree.setColumnWidth(i, width)
            
            # 修改时间列保持显示，但宽度较小，通过滚动条查看
            self.files_tree.setColumnWidth(3, 60)
            
            # 保持所有列的交互式调整能力
            # 这里不再设置固定的列宽模式，让用户可以自由拖动调整
            
            # 恢复正常光标
            QApplication.restoreOverrideCursor()
            
        except Exception as e:
            # 确保出错时也恢复光标
            QApplication.restoreOverrideCursor()
            print(f"更新文件列表出错: {str(e)}")
            
    def refresh_files_list(self):
        """手动刷新文件列表"""
        if self.current_folder and os.path.exists(self.current_folder):
            self.update_files_list(self.current_folder)
            
    def auto_refresh_files_list(self):
        """自动刷新文件列表"""
        if not self.auto_refresh_checkbox.isChecked() or not self.current_folder:
            return
            
        try:
            if os.path.exists(self.current_folder):
                self.update_files_list(self.current_folder)
        except Exception as e:
            print(f"自动刷新出错: {str(e)}")

    def init_ui_components(self):
        """初始化所有UI组件"""
        # 创建主内容区域
        self.main_content = QWidget()
        self.main_content_layout = QHBoxLayout(self.main_content)
        self.main_content_layout.setContentsMargins(0, 0, 0, 0)
        self.main_content_layout.setSpacing(12)
        
        # 创建左右面板
        self.left_panel = self.create_left_panel()
        self.right_panel = self.create_right_panel()
        
        # 添加左右面板到主内容布局 - 调整比例使视图更协调
        self.main_content_layout.addWidget(self.left_panel, 55)  # 左侧适当宽度
        self.main_content_layout.addWidget(self.right_panel, 45)  # 右侧宽度进一步增加
        
        # 将主内容添加到主布局
        self.main_layout.addWidget(self.main_content)
        
        # 连接刷新按钮
        self.refresh_button.clicked.connect(self.refresh_files_list)
        
        # 连接按钮事件
        self.cancel_button.clicked.connect(self.reject)
        self.ok_button.clicked.connect(self.start_compression)
        self.pack_and_screenshot_button.clicked.connect(lambda: self.parent_window.take_screenshot(True))
        
        # 连接浏览按钮
        browse_button = self.findChild(QPushButton, "browse_button")
        if browse_button:
            browse_button.clicked.connect(self.browse_output_path)
        
    def create_title_bar(self):
        """创建标题栏"""
        title_bar = QWidget()
        title_bar.setFixedHeight(42)  # 增加高度
        title_bar.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #e0e7ff,
                    stop:0.4 #c7d2fe,
                    stop:1 #a5b4fc);
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                border-bottom: 1px solid #818cf8;
            }
        """)
        
        # 添加玻璃态效果的阴影
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(12)
        shadow.setColor(QColor(0, 0, 0, 15))
        shadow.setOffset(0, 1)
        title_bar.setGraphicsEffect(shadow)
        
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(12, 0, 12, 0)
        title_layout.setSpacing(8)
        
        # 添加图标
        icon_label = QLabel()
        icon_label.setFixedSize(24, 24)  # 增加图标尺寸
        icon_label.setPixmap(self.create_app_icon().scaled(24, 24, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        
        # 为图标添加阴影效果
        icon_shadow = QGraphicsDropShadowEffect()
        icon_shadow.setBlurRadius(4)
        icon_shadow.setColor(QColor(0, 0, 0, 20))
        icon_shadow.setOffset(0, 1)
        icon_label.setGraphicsEffect(icon_shadow)
        
        title_layout.addWidget(icon_label)
        
        # 添加标题文本
        title_text = QLabel("视频文件")
        title_text.setStyleSheet("""
            font-family: 'Microsoft YaHei UI';
            font-size: 15px;
            font-weight: 600;
            color: #312e81;
            padding: 0px;
            background: transparent;
            text-shadow: 0px 1px 1px rgba(255, 255, 255, 0.7);
        """)
        title_layout.addWidget(title_text)
        
        title_layout.addStretch()
        
        return title_bar
    
    def create_left_panel(self):
        """创建现代化左侧面板"""
        left_panel = QWidget()
        left_panel.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
            }
        """)

        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(16, 16, 8, 16)
        left_layout.setSpacing(16)
        
        # 现代化视频信息卡片区域
        stats_title = QLabel("📊 文件统计")
        stats_title.setStyleSheet("""
            QLabel {
                color: #2d3748;
                font-size: 16px;
                font-weight: 600;
                background: transparent;
                border: none;
                margin-bottom: 8px;
            }
        """)
        left_layout.addWidget(stats_title)

        # 创建统计卡片容器
        stats_container = QWidget()
        stats_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.5 rgba(248, 250, 252, 0.95),
                    stop:1 rgba(241, 245, 249, 0.9));
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 16px;
                backdrop-filter: blur(10px);
            }
        """)

        # 添加统计卡片阴影
        stats_shadow = QGraphicsDropShadowEffect()
        stats_shadow.setBlurRadius(20)
        stats_shadow.setColor(QColor(102, 126, 234, 40))
        stats_shadow.setOffset(0, 4)
        stats_container.setGraphicsEffect(stats_shadow)

        # 创建统计卡片内部布局
        stats_layout = QHBoxLayout(stats_container)
        stats_layout.setContentsMargins(20, 16, 20, 16)
        stats_layout.setSpacing(24)

        # 视频数量统计卡片
        video_stat_card = self.create_stat_card("🎬", "视频文件", "0个", "#667eea")
        self.video_count_label = video_stat_card.findChild(QLabel, "value_label")
        stats_layout.addWidget(video_stat_card)

        # 文件大小统计卡片
        size_stat_card = self.create_stat_card("💾", "总大小", "0 GB", "#f093fb")
        self.total_size_label = size_stat_card.findChild(QLabel, "value_label")
        stats_layout.addWidget(size_stat_card)

        # 总时长统计卡片
        duration_stat_card = self.create_stat_card("⏱️", "总时长", "0:00", "#4facfe")
        self.total_duration_label = duration_stat_card.findChild(QLabel, "value_label")
        stats_layout.addWidget(duration_stat_card)

        left_layout.addWidget(stats_container)

        # 现代化文件列表区域
        files_title = QLabel("📁 文件浏览")
        files_title.setStyleSheet("""
            QLabel {
                color: #2d3748;
                font-size: 16px;
                font-weight: 600;
                background: transparent;
                border: none;
                margin-top: 16px;
                margin-bottom: 8px;
            }
        """)
        left_layout.addWidget(files_title)

        # 创建文件列表容器
        files_container = QWidget()
        files_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(248, 250, 252, 0.8));
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 16px;
                backdrop-filter: blur(10px);
            }
        """)

        # 添加文件容器阴影
        files_shadow = QGraphicsDropShadowEffect()
        files_shadow.setBlurRadius(20)
        files_shadow.setColor(QColor(102, 126, 234, 30))
        files_shadow.setOffset(0, 4)
        files_container.setGraphicsEffect(files_shadow)

        files_layout = QVBoxLayout(files_container)
        files_layout.setContentsMargins(16, 16, 16, 16)
        files_layout.setSpacing(12)

        # 文件列表控制栏
        files_header = QHBoxLayout()
        files_header.setSpacing(12)

        # 文件计数标签
        self.files_count_label = QLabel("共0个文件")
        self.files_count_label.setStyleSheet("""
            QLabel {
                color: #4a5568;
                font-size: 14px;
                font-weight: 500;
                background: transparent;
                border: none;
            }
        """)
        files_header.addWidget(self.files_count_label)
        files_header.addStretch()

        # 自动刷新开关
        self.auto_refresh_checkbox = QCheckBox("自动刷新")
        self.auto_refresh_checkbox.setStyleSheet(self.checkbox_style)
        files_header.addWidget(self.auto_refresh_checkbox)

        # 刷新按钮
        self.refresh_button = QPushButton("🔄 刷新")
        self.refresh_button.setFixedSize(80, 32)
        self.refresh_button.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: 500;
                color: #4a5568;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.8),
                    stop:1 rgba(248, 250, 252, 0.9));
                border: 1px solid rgba(255, 255, 255, 0.5);
                border-radius: 8px;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.95));
                border-color: #667eea;
                color: #2d3748;
            }
            QPushButton:pressed {
                background: rgba(102, 126, 234, 0.1);
                transform: scale(0.98);
            }
        """)
        files_header.addWidget(self.refresh_button)

        files_layout.addLayout(files_header)

        # 现代化文件树
        self.files_tree = QTreeWidget()
        self.files_tree.setHeaderLabels(["文件名", "大小", "时长", "修改时间"])
        self.files_tree.setStyleSheet("""
            QTreeWidget {
                background: rgba(255, 255, 255, 0.7);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                font-size: 13px;
                color: #2d3748;
                selection-background-color: rgba(102, 126, 234, 0.2);
                alternate-background-color: rgba(248, 250, 252, 0.5);
            }
            QTreeWidget::item {
                padding: 8px 4px;
                border-radius: 6px;
                margin: 1px;
            }
            QTreeWidget::item:hover {
                background: rgba(102, 126, 234, 0.1);
            }
            QTreeWidget::item:selected {
                background: rgba(102, 126, 234, 0.2);
                border: 1px solid rgba(102, 126, 234, 0.3);
            }
            QTreeWidget QHeaderView::section {
                background: rgba(248, 250, 252, 0.8);
                border: none;
                border-bottom: 1px solid rgba(226, 232, 240, 0.5);
                padding: 8px 12px;
                font-weight: 600;
                color: #4a5568;
                font-size: 12px;
            }
            QScrollBar:vertical {
                background: rgba(241, 245, 249, 0.5);
                width: 8px;
                border-radius: 4px;
                border: none;
            }
            QScrollBar::handle:vertical {
                background: rgba(102, 126, 234, 0.3);
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(102, 126, 234, 0.5);
            }
            QScrollBar::add-line:vertical,
            QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)

        files_layout.addWidget(self.files_tree)
        left_layout.addWidget(files_container)

        return left_panel

    def create_stat_card(self, icon, title, value, color):
        """创建统计信息卡片"""
        card = QWidget()
        card.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(248, 250, 252, 0.8));
                border: 1px solid rgba(255, 255, 255, 0.5);
                border-radius: 12px;
                min-height: 80px;
            }}
            QWidget:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(241, 245, 249, 0.9));
                border-color: {color};
                transform: translateY(-2px);
            }}
        """)

        # 添加卡片阴影
        card_shadow = QGraphicsDropShadowEffect()
        card_shadow.setBlurRadius(15)
        card_shadow.setColor(QColor(0, 0, 0, 20))
        card_shadow.setOffset(0, 3)
        card.setGraphicsEffect(card_shadow)

        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(16, 12, 16, 12)
        card_layout.setSpacing(8)
        card_layout.setAlignment(Qt.AlignCenter)

        # 图标和标题行
        header_layout = QHBoxLayout()
        header_layout.setSpacing(8)
        header_layout.setAlignment(Qt.AlignCenter)

        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 20px;
                color: {color};
                background: transparent;
                border: none;
            }}
        """)
        header_layout.addWidget(icon_label)

        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #4a5568;
                font-size: 12px;
                font-weight: 500;
                background: transparent;
                border: none;
            }
        """)
        header_layout.addWidget(title_label)

        card_layout.addLayout(header_layout)

        # 数值标签
        value_label = QLabel(value)
        value_label.setObjectName("value_label")
        value_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: 16px;
                font-weight: 700;
                background: transparent;
                border: none;
            }}
        """)
        value_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(value_label)

        return card

    def create_right_panel(self):
        """创建现代化右侧面板"""
        right_panel = QWidget()
        right_panel.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
            }
        """)

        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(8, 16, 16, 16)
        right_layout.setSpacing(16)

        # 压缩设置标题
        settings_title = QLabel("⚙️ 压缩设置")
        settings_title.setStyleSheet("""
            QLabel {
                color: #2d3748;
                font-size: 16px;
                font-weight: 600;
                background: transparent;
                border: none;
                margin-bottom: 8px;
            }
        """)
        right_layout.addWidget(settings_title)

        # 创建现代化设置卡片容器
        settings_container = QWidget()
        settings_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(248, 250, 252, 0.8));
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 16px;
                backdrop-filter: blur(10px);
            }
        """)

        # 添加设置容器阴影
        settings_shadow = QGraphicsDropShadowEffect()
        settings_shadow.setBlurRadius(20)
        settings_shadow.setColor(QColor(102, 126, 234, 30))
        settings_shadow.setOffset(0, 4)
        settings_container.setGraphicsEffect(settings_shadow)

        settings_layout = QVBoxLayout(settings_container)
        settings_layout.setContentsMargins(20, 20, 20, 20)
        settings_layout.setSpacing(16)

        # 基本设置区域
        basic_settings = self.create_basic_settings_section()
        settings_layout.addWidget(basic_settings)

        # 高级设置区域
        advanced_settings = self.create_advanced_settings_section()
        settings_layout.addWidget(advanced_settings)

        # 进度显示区域
        progress_section = self.create_progress_section()
        settings_layout.addWidget(progress_section)

        # 密码提示区域
        password_section = self.create_password_section()
        settings_layout.addWidget(password_section)

        # 按钮区域
        buttons_section = self.create_buttons_section()
        settings_layout.addWidget(buttons_section)

        right_layout.addWidget(settings_container)

        return right_panel

    def create_basic_settings_section(self):
        """创建基本设置区域"""
        section = QWidget()
        section.setStyleSheet("""
            QWidget {
                background: rgba(255, 255, 255, 0.5);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                padding: 4px;
            }
        """)

        layout = QVBoxLayout(section)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # 区域标题
        title = QLabel("📁 基本设置")
        title.setStyleSheet("""
            QLabel {
                color: #4a5568;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(title)

        # 保存位置设置
        path_container = QWidget()
        path_container.setStyleSheet("background: transparent; border: none;")
        path_layout = QHBoxLayout(path_container)
        path_layout.setContentsMargins(0, 0, 0, 0)
        path_layout.setSpacing(8)

        path_label = QLabel("保存位置:")
        path_label.setFixedWidth(70)
        path_label.setStyleSheet("""
            QLabel {
                color: #4a5568;
                font-size: 12px;
                font-weight: 500;
                background: transparent;
                border: none;
            }
        """)
        path_layout.addWidget(path_label)

        self.output_path = QLineEdit()
        self.output_path.setFixedHeight(32)
        self.output_path.setStyleSheet("""
            QLineEdit {
                font-size: 12px;
                padding: 6px 12px;
                border: 1px solid rgba(226, 232, 240, 0.8);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.8);
                color: #2d3748;
            }
            QLineEdit:focus {
                border-color: #667eea;
                background: rgba(255, 255, 255, 0.95);
                outline: none;
            }
            QLineEdit:hover {
                border-color: #a78bfa;
                background: rgba(255, 255, 255, 0.9);
            }
        """)
        path_layout.addWidget(self.output_path)

        browse_button = QPushButton("📂")
        browse_button.setFixedSize(32, 32)
        browse_button.setObjectName("browse_button")
        browse_button.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                border: 1px solid rgba(226, 232, 240, 0.8);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.8);
                color: #4a5568;
            }
            QPushButton:hover {
                background: rgba(102, 126, 234, 0.1);
                border-color: #667eea;
                color: #2d3748;
            }
            QPushButton:pressed {
                background: rgba(102, 126, 234, 0.2);
                transform: scale(0.98);
            }
        """)
        path_layout.addWidget(browse_button)

        layout.addWidget(path_container)

        # 压缩级别设置
        level_container = QWidget()
        level_container.setStyleSheet("background: transparent; border: none;")
        level_layout = QHBoxLayout(level_container)
        level_layout.setContentsMargins(0, 0, 0, 0)
        level_layout.setSpacing(8)

        level_label = QLabel("压缩级别:")
        level_label.setFixedWidth(70)
        level_label.setStyleSheet("""
            QLabel {
                color: #4a5568;
                font-size: 12px;
                font-weight: 500;
                background: transparent;
                border: none;
            }
        """)
        level_layout.addWidget(level_label)

        self.compression_level = QComboBox()
        self.compression_level.setFixedHeight(32)
        self.compression_level.setStyleSheet("""
            QComboBox {
                font-size: 12px;
                padding: 6px 12px;
                border: 1px solid rgba(226, 232, 240, 0.8);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.8);
                color: #2d3748;
            }
            QComboBox:hover {
                background: rgba(255, 255, 255, 0.9);
                border-color: #a78bfa;
            }
            QComboBox::drop-down {
                border: none;
                width: 24px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
        """)
        self.compression_level.addItems(["存储", "最快", "快速", "标准", "最大"])
        self.compression_level.setCurrentIndex(0)
        level_layout.addWidget(self.compression_level)
        level_layout.addStretch()

        layout.addWidget(level_container)

        # 密码保护设置
        password_container = QWidget()
        password_container.setStyleSheet("background: transparent; border: none;")
        password_layout = QVBoxLayout(password_container)
        password_layout.setContentsMargins(0, 0, 0, 0)
        password_layout.setSpacing(8)

        self.use_password = QCheckBox("🔒 设置密码保护")
        self.use_password.setChecked(True)
        self.use_password.setStyleSheet(self.checkbox_style)
        password_layout.addWidget(self.use_password)

        # 密码输入框
        pw_input_container = QWidget()
        pw_input_container.setStyleSheet("background: transparent; border: none;")
        pw_input_layout = QHBoxLayout(pw_input_container)
        pw_input_layout.setContentsMargins(20, 0, 0, 0)
        pw_input_layout.setSpacing(8)

        pw_label = QLabel("密码:")
        pw_label.setFixedWidth(50)
        pw_label.setStyleSheet("""
            QLabel {
                color: #4a5568;
                font-size: 12px;
                font-weight: 500;
                background: transparent;
                border: none;
            }
        """)
        pw_input_layout.addWidget(pw_label)

        self.password = QLineEdit()
        self.password.setFixedHeight(32)
        self.password.setEchoMode(QLineEdit.Normal)
        self.password.setText("2048论坛")
        self.password.setPlaceholderText("输入密码")
        self.password.setStyleSheet("""
            QLineEdit {
                font-size: 12px;
                padding: 6px 12px;
                border: 1px solid rgba(226, 232, 240, 0.8);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.8);
                color: #2d3748;
            }
            QLineEdit:focus {
                border-color: #667eea;
                background: rgba(255, 255, 255, 0.95);
                outline: none;
            }
            QLineEdit:hover {
                border-color: #a78bfa;
                background: rgba(255, 255, 255, 0.9);
            }
        """)
        pw_input_layout.addWidget(self.password)

        password_layout.addWidget(pw_input_container)
        layout.addWidget(password_container)

        return section

    def create_advanced_settings_section(self):
        """创建高级设置区域"""
        section = QWidget()
        section.setStyleSheet("""
            QWidget {
                background: rgba(255, 255, 255, 0.5);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                padding: 4px;
            }
        """)

        layout = QVBoxLayout(section)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # 区域标题
        title = QLabel("⚡ 高级选项")
        title.setStyleSheet("""
            QLabel {
                color: #4a5568;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(title)

        # 压缩格式选择
        format_container = QWidget()
        format_container.setStyleSheet("background: transparent; border: none;")
        format_layout = QVBoxLayout(format_container)
        format_layout.setContentsMargins(0, 0, 0, 0)
        format_layout.setSpacing(8)

        format_label = QLabel("压缩格式:")
        format_label.setStyleSheet("""
            QLabel {
                color: #4a5568;
                font-size: 12px;
                font-weight: 500;
                background: transparent;
                border: none;
            }
        """)
        format_layout.addWidget(format_label)

        # 格式选择按钮组
        format_buttons_container = QWidget()
        format_buttons_container.setStyleSheet("background: transparent; border: none;")
        format_buttons_layout = QHBoxLayout(format_buttons_container)
        format_buttons_layout.setContentsMargins(0, 0, 0, 0)
        format_buttons_layout.setSpacing(8)

        self.format_group = QButtonGroup(self)

        # 现代化单选按钮样式
        radio_style = """
            QRadioButton {
                font-size: 12px;
                color: #4a5568;
                background: rgba(255, 255, 255, 0.6);
                border: 1px solid rgba(226, 232, 240, 0.8);
                border-radius: 8px;
                padding: 6px 12px;
                font-weight: 500;
            }
            QRadioButton:hover {
                background: rgba(102, 126, 234, 0.1);
                border-color: #667eea;
                color: #2d3748;
            }
            QRadioButton:checked {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea,
                    stop:1 #764ba2);
                border-color: #667eea;
                color: white;
            }
            QRadioButton::indicator {
                width: 0px;
                height: 0px;
            }
        """

        self.rar_radio = QRadioButton("RAR")
        self.rar_radio.setChecked(True)
        self.rar_radio.setStyleSheet(radio_style)
        self.format_group.addButton(self.rar_radio)
        format_buttons_layout.addWidget(self.rar_radio)

        self.rar4_radio = QRadioButton("RAR4")
        self.rar4_radio.setStyleSheet(radio_style)
        self.format_group.addButton(self.rar4_radio)
        format_buttons_layout.addWidget(self.rar4_radio)

        self.zip_radio = QRadioButton("ZIP")
        self.zip_radio.setStyleSheet(radio_style)
        self.format_group.addButton(self.zip_radio)
        format_buttons_layout.addWidget(self.zip_radio)

        format_buttons_layout.addStretch()
        format_layout.addWidget(format_buttons_container)
        layout.addWidget(format_container)

        # 其他选项
        options_container = QWidget()
        options_container.setStyleSheet("background: transparent; border: none;")
        options_layout = QVBoxLayout(options_container)
        options_layout.setContentsMargins(0, 0, 0, 0)
        options_layout.setSpacing(8)

        # 第一行选项
        row1_container = QWidget()
        row1_container.setStyleSheet("background: transparent; border: none;")
        row1_layout = QHBoxLayout(row1_container)
        row1_layout.setContentsMargins(0, 0, 0, 0)
        row1_layout.setSpacing(16)

        self.enable_split = QCheckBox("📦 启用分卷")
        self.enable_split.setChecked(False)
        self.enable_split.setStyleSheet(self.checkbox_style)
        row1_layout.addWidget(self.enable_split)

        # 分卷数量输入
        split_container = QWidget()
        split_container.setStyleSheet("background: transparent; border: none;")
        split_layout = QHBoxLayout(split_container)
        split_layout.setContentsMargins(0, 0, 0, 0)
        split_layout.setSpacing(4)

        self.split_count = QLineEdit()
        self.split_count.setFixedWidth(40)
        self.split_count.setFixedHeight(24)
        self.split_count.setText("2")
        self.split_count.setEnabled(False)
        self.split_count.setAlignment(Qt.AlignCenter)
        self.split_count.setStyleSheet("""
            QLineEdit {
                font-size: 11px;
                padding: 2px 4px;
                border: 1px solid rgba(226, 232, 240, 0.8);
                border-radius: 6px;
                background: rgba(255, 255, 255, 0.8);
                color: #4a5568;
            }
            QLineEdit:disabled {
                background: rgba(248, 250, 252, 0.5);
                color: #a0aec0;
            }
        """)
        split_layout.addWidget(self.split_count)

        split_unit = QLabel("个文件")
        split_unit.setStyleSheet("""
            QLabel {
                color: #4a5568;
                font-size: 11px;
                background: transparent;
                border: none;
            }
        """)
        split_layout.addWidget(split_unit)

        row1_layout.addWidget(split_container)
        row1_layout.addStretch()

        options_layout.addWidget(row1_container)

        # 第二行选项
        row2_container = QWidget()
        row2_container.setStyleSheet("background: transparent; border: none;")
        row2_layout = QHBoxLayout(row2_container)
        row2_layout.setContentsMargins(0, 0, 0, 0)
        row2_layout.setSpacing(16)

        self.create_recovery = QCheckBox("🔧 创建恢复记录")
        self.create_recovery.setStyleSheet(self.checkbox_style)
        row2_layout.addWidget(self.create_recovery)

        self.test_archive = QCheckBox("✅ 测试压缩文件")
        self.test_archive.setChecked(True)
        self.test_archive.setStyleSheet(self.checkbox_style)
        row2_layout.addWidget(self.test_archive)

        row2_layout.addStretch()

        options_layout.addWidget(row2_container)
        layout.addWidget(options_container)

        # 添加自动刷新变量但不显示
        self.auto_refresh = QCheckBox("自动刷新")
        self.auto_refresh.setVisible(False)
        self.auto_refresh.setChecked(False)

        # 连接信号
        self.enable_split.stateChanged.connect(self.toggle_split_count)
        self.auto_refresh.stateChanged.connect(self.toggle_auto_refresh)

        return section

    def create_progress_section(self):
        """创建进度显示区域"""
        section = QWidget()
        section.setStyleSheet("""
            QWidget {
                background: rgba(255, 255, 255, 0.5);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                padding: 4px;
            }
        """)

        layout = QVBoxLayout(section)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(8)

        # 状态显示
        status_container = QWidget()
        status_container.setStyleSheet("background: transparent; border: none;")
        status_layout = QHBoxLayout(status_container)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setSpacing(8)

        self.progress_status = QLabel("📊 压缩完成")
        self.progress_status.setStyleSheet("""
            QLabel {
                color: #4a5568;
                font-size: 13px;
                font-weight: 500;
                background: transparent;
                border: none;
            }
        """)
        status_layout.addWidget(self.progress_status)

        status_layout.addStretch()

        self.progress_percent = QLabel("100%")
        self.progress_percent.setStyleSheet("""
            QLabel {
                color: #667eea;
                font-size: 13px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        """)
        status_layout.addWidget(self.progress_percent)

        layout.addWidget(status_container)

        # 现代化进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(6)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                background: rgba(241, 245, 249, 0.8);
                border: none;
                border-radius: 3px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea,
                    stop:0.5 #764ba2,
                    stop:1 #f093fb);
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.progress_bar)

        return section

    def create_password_section(self):
        """创建密码提示区域"""
        section = QWidget()
        section.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 154, 158, 0.1),
                    stop:1 rgba(250, 208, 196, 0.1));
                border: 1px solid rgba(255, 154, 158, 0.2);
                border-radius: 12px;
                padding: 4px;
            }
        """)

        layout = QHBoxLayout(section)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(12)
        layout.setAlignment(Qt.AlignCenter)

        # 密码图标
        icon_label = QLabel("🔑")
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(icon_label)

        # 密码提示文本
        title_label = QLabel("解压密码")
        title_label.setStyleSheet("""
            QLabel {
                color: #4a5568;
                font-size: 13px;
                font-weight: 500;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(title_label)

        # 密码值容器
        password_value_container = QWidget()
        password_value_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff9a9e,
                    stop:1 #fad0c4);
                border: none;
                border-radius: 8px;
                padding: 2px 8px;
            }
        """)

        password_layout = QHBoxLayout(password_value_container)
        password_layout.setContentsMargins(8, 4, 8, 4)
        password_layout.setSpacing(0)

        password_label = QLabel("2048论坛")
        password_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                font-weight: 700;
                background: transparent;
                border: none;
                text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3);
            }
        """)
        password_layout.addWidget(password_label)

        layout.addWidget(password_value_container)

        return section

    def create_buttons_section(self):
        """创建按钮区域"""
        section = QWidget()
        section.setStyleSheet("background: transparent; border: none;")

        layout = QHBoxLayout(section)
        layout.setContentsMargins(0, 8, 0, 0)
        layout.setSpacing(12)

        # 现代化按钮样式
        button_style = """
            QPushButton {
                font-size: 13px;
                font-weight: 600;
                padding: 10px 20px;
                border: none;
                border-radius: 10px;
                color: white;
                min-width: 80px;
            }
        """

        # 截图按钮
        self.pack_and_screenshot_button = QPushButton("📸 截图")
        self.pack_and_screenshot_button.setStyleSheet(button_style + """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4facfe,
                    stop:1 #00f2fe);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00f2fe,
                    stop:1 #4facfe);
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                transform: translateY(0px) scale(0.98);
            }
        """)

        # 添加按钮阴影
        screenshot_shadow = QGraphicsDropShadowEffect()
        screenshot_shadow.setBlurRadius(15)
        screenshot_shadow.setColor(QColor(79, 172, 254, 100))
        screenshot_shadow.setOffset(0, 3)
        self.pack_and_screenshot_button.setGraphicsEffect(screenshot_shadow)

        layout.addWidget(self.pack_and_screenshot_button)

        # 取消按钮
        self.cancel_button = QPushButton("❌ 取消")
        self.cancel_button.setStyleSheet(button_style + """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff9a9e,
                    stop:1 #fecfef);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fecfef,
                    stop:1 #ff9a9e);
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                transform: translateY(0px) scale(0.98);
            }
        """)

        # 添加按钮阴影
        cancel_shadow = QGraphicsDropShadowEffect()
        cancel_shadow.setBlurRadius(15)
        cancel_shadow.setColor(QColor(255, 154, 158, 100))
        cancel_shadow.setOffset(0, 3)
        self.cancel_button.setGraphicsEffect(cancel_shadow)

        layout.addWidget(self.cancel_button)

        # 开始按钮
        self.ok_button = QPushButton("🚀 开始")
        self.ok_button.setStyleSheet(button_style + """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea,
                    stop:1 #764ba2);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #764ba2,
                    stop:1 #667eea);
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                transform: translateY(0px) scale(0.98);
            }
        """)

        # 添加按钮阴影
        ok_shadow = QGraphicsDropShadowEffect()
        ok_shadow.setBlurRadius(15)
        ok_shadow.setColor(QColor(102, 126, 234, 100))
        ok_shadow.setOffset(0, 3)
        self.ok_button.setGraphicsEffect(ok_shadow)

        layout.addWidget(self.ok_button)

        return section

    def create_right_panel_old(self):
        """创建右侧面板"""
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(8)
        
        # 设置右侧面板最小宽度，确保参数完整显示
        right_panel.setMinimumWidth(350)
        
        # 压缩设置区域
        settings_group = QGroupBox("压缩设置")
        settings_group.setStyleSheet("""
            QGroupBox {
                font-size: 12px;
                font-weight: 500;
                color: #1e40af;  /* 修改为深蓝色 */
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                margin-top: 12px;
                background-color: #ffffff;
                padding: 6px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 4px;
                background-color: #ffffff;
            }
        """)
        
        # 设置右侧面板的最小高度 - 减小高度以适应新布局
        settings_group.setMinimumHeight(280)
        
        settings_layout = QVBoxLayout(settings_group)
        settings_layout.setContentsMargins(6, 14, 6, 6)  # 减小边距
        settings_layout.setSpacing(4)  # 减小间距
        
        # 输出文件名设置
        path_grid = QGridLayout()
        path_grid.setSpacing(8)
        
        path_label = QLabel("保存位置:")
        path_label.setFixedWidth(70)
        path_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        path_label.setStyleSheet("""
            font-size: 12px;
            color: #475569;
        """)
        
        self.output_path = QLineEdit()
        self.output_path.setFixedHeight(32)
        self.output_path.setStyleSheet("""
            QLineEdit {
                font-size: 12px;
                padding: 4px 8px;
                border: 1px solid #e2e8f0;
                border-radius: 4px;
                background: #f8fafc;
            }
            QLineEdit:focus {
                border-color: #3b82f6;
                background: white;
            }
        """)
        
        browse_button = QPushButton("浏览")
        browse_button.setFixedSize(60, 32)
        browse_button.setObjectName("browse_button")
        browse_button.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                padding: 4px 12px;
                border: 1px solid #e2e8f0;
                border-radius: 4px;
                background: #f8fafc;
                color: #475569;
            }
            QPushButton:hover {
                background: #f1f5f9;
                border-color: #cbd5e1;
            }
        """)
        
        path_grid.addWidget(path_label, 0, 0)
        path_grid.addWidget(self.output_path, 0, 1)
        path_grid.addWidget(browse_button, 0, 2)
        
        settings_layout.addLayout(path_grid)
        
        # 压缩级别设置
        level_grid = QGridLayout()
        level_grid.setSpacing(8)
        
        level_label = QLabel("压缩级别:")
        level_label.setFixedWidth(70)
        level_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        level_label.setStyleSheet("""
            font-size: 12px;
            color: #475569;
        """)
        
        self.compression_level = QComboBox()
        self.compression_level.setFixedHeight(32)
        self.compression_level.setStyleSheet("""
            QComboBox {
                font-size: 12px;
                padding: 4px 8px;
                border: 1px solid #e2e8f0;
                border-radius: 4px;
                background: #f8fafc;
            }
            QComboBox:hover {
                background: #ffffff;
                border-color: #93c5fd;
            }
            QComboBox::drop-down {
                border: none;
                width: 24px;
            }
        """)
        self.compression_level.addItems(["存储", "最快", "快速", "标准", "最大"])
        self.compression_level.setCurrentIndex(0)
        
        level_grid.addWidget(level_label, 0, 0)
        level_grid.addWidget(self.compression_level, 0, 1)
        level_grid.addItem(QSpacerItem(60, 32), 0, 2)
        
        settings_layout.addLayout(level_grid)
        
        # 密码保护设置
        pw_grid = QGridLayout()
        pw_grid.setSpacing(8)
        
        # 创建密码保护复选框 - 使用系统原生勾选样式
        self.use_password = QCheckBox("设置密码保护")
        self.use_password.setChecked(True)
        self.use_password.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                color: #475569;
                margin-left: 70px;
            }
        """)
        pw_grid.addWidget(self.use_password, 0, 0, 1, 3)
        
        # 密码输入框
        pw_label = QLabel("密码:")
        pw_label.setFixedWidth(70)
        pw_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        pw_label.setStyleSheet("""
            font-size: 12px;
            color: #475569;
        """)
        
        self.password = QLineEdit()
        self.password.setFixedHeight(32)
        self.password.setEchoMode(QLineEdit.Normal)
        self.password.setText("2048论坛")
        self.password.setPlaceholderText("输入密码")
        self.password.setStyleSheet("""
            QLineEdit {
                font-size: 12px;
                padding: 4px 8px;
                border: 1px solid #d1d5db;
                border-radius: 4px;
                background: white;
            }
            QLineEdit:focus {
                border-color: #4f46e5;
                outline: none;
            }
            QLineEdit:hover {
                border-color: #4f46e5;
            }
        """)
        
        pw_grid.addWidget(pw_label, 1, 0)
        pw_grid.addWidget(self.password, 1, 1, 1, 2)
        
        settings_layout.addLayout(pw_grid)
        
        # 压缩状态显示区域
        status_container = QWidget()
        status_container.setStyleSheet("""
            QWidget {
            background: transparent;
            border: none;
            }
        """)
        status_layout = QHBoxLayout(status_container)
        status_layout.setContentsMargins(0, 4, 0, 4)
        status_layout.setSpacing(4)

        # 状态文本
        self.progress_status = QLabel("压缩完成")
        self.progress_status.setStyleSheet("""
            font-size: 13px;
            color: #1e40af;
        """)
        status_layout.addWidget(self.progress_status)

        # 进度百分比
        self.progress_percent = QLabel("100%")
        self.progress_percent.setStyleSheet("""
            font-size: 13px;
            color: #1e40af;
        """)
        status_layout.addWidget(self.progress_percent)

        status_layout.addStretch()

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(4)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                background: #f1f5f9;
                border: none;
                border-radius: 2px;
                margin-top: 8px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #60a5fa,
                    stop:1 #2563eb);
                border-radius: 2px;
            }
        """)

        # 创建垂直布局来放置状态容器和进度条
        progress_container = QWidget()
        progress_container.setStyleSheet("""
            QWidget {
                background: white;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                margin: 4px 0;
            }
        """)
        progress_layout = QVBoxLayout(progress_container)
        progress_layout.setContentsMargins(12, 8, 12, 8)
        progress_layout.setSpacing(0)
        
        progress_layout.addWidget(status_container)
        progress_layout.addWidget(self.progress_bar)
        
        settings_layout.addWidget(progress_container)
        
        # 添加一个较小的弹性空间
        settings_layout.addItem(QSpacerItem(20, 6, QSizePolicy.Minimum, QSizePolicy.Fixed))
        
        # 高级设置区域 - 三行显示
        advanced_group = QGroupBox("高级设置")
        advanced_group.setStyleSheet("""
            QGroupBox {
                font-size: 12px;
                font-weight: 500;
                color: #1e40af;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                margin-top: 10px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 4px;
                background-color: #ffffff;
            }
        """)
        
        advanced_layout = QVBoxLayout(advanced_group)
        advanced_layout.setContentsMargins(10, 16, 10, 8)
        advanced_layout.setSpacing(6)
        
        # 创建统一的复选框样式
        checkbox_style = """
            QCheckBox {
                font-size: 12px;
                color: #334155;
            }
            QCheckBox:hover {
                color: #1e40af;
            }
            QCheckBox:disabled {
                color: #94a3b8;
            }
        """
        
        # 单选按钮样式
        radio_style = """
            QRadioButton {
            font-size: 12px;
                color: #334155;
            }
            QRadioButton:hover {
                color: #1e40af;
            }
        """
        
        # 第一行 - 压缩文件格式选择
        row1 = QHBoxLayout()
        row1.setContentsMargins(0, 0, 0, 0)
        row1.setSpacing(15)
        
        # 添加格式标签
        format_label = QLabel("压缩文件格式:")
        format_label.setStyleSheet("font-size: 12px; color: #334155;")
        row1.addWidget(format_label)
        
        # 创建单选按钮组
        self.format_group = QButtonGroup(self)
        
        # RAR格式单选按钮
        self.rar_radio = QRadioButton("RAR")
        self.rar_radio.setChecked(True)  # 默认选择RAR
        self.rar_radio.setStyleSheet(radio_style)
        self.format_group.addButton(self.rar_radio)
        row1.addWidget(self.rar_radio)
        
        # RAR4格式单选按钮
        self.rar4_radio = QRadioButton("RAR4")
        self.rar4_radio.setStyleSheet(radio_style)
        self.format_group.addButton(self.rar4_radio)
        row1.addWidget(self.rar4_radio)
        
        # ZIP格式单选按钮
        self.zip_radio = QRadioButton("ZIP")
        self.zip_radio.setStyleSheet(radio_style)
        self.format_group.addButton(self.zip_radio)
        row1.addWidget(self.zip_radio)
        
        row1.addStretch(1)
        
        # 添加第一行到主布局
        advanced_layout.addLayout(row1)
        
        # 第二行 - 分卷和测试
        row2 = QHBoxLayout()
        row2.setContentsMargins(0, 0, 0, 0)
        row2.setSpacing(15)
        
        # 智能分卷复选框
        self.enable_split = QCheckBox("启用分卷")
        self.enable_split.setChecked(False)
        self.enable_split.setStyleSheet(checkbox_style)
        row2.addWidget(self.enable_split)
        
        # 分卷数量输入框
        self.split_count = QLineEdit()
        self.split_count.setFixedWidth(30)
        self.split_count.setText("2")
        self.split_count.setEnabled(False)
        self.split_count.setAlignment(Qt.AlignCenter)
        self.split_count.setStyleSheet("""
            QLineEdit {
                padding: 2px;
                border: 1px solid #d1d5db;
                border-radius: 3px;
                background: white;
                color: #475569;
                font-size: 12px;
            }
            QLineEdit:disabled {
                background: #f1f5f9;
                color: #94a3b8;
            }
        """)
        row2.addWidget(self.split_count)
        
        # 个文件标签
        split_unit = QLabel("个文件")
        split_unit.setStyleSheet("font-size: 12px; color: #334155;")
        row2.addWidget(split_unit)
        
        # 添加间隔
        row2.addSpacing(20)
        
        row2.addStretch(1)
        
        # 添加第二行到主布局
        advanced_layout.addLayout(row2)
        
        # 第三行 - 恢复记录和测试压缩文件
        row3 = QHBoxLayout()
        row3.setContentsMargins(0, 0, 0, 0)
        row3.setSpacing(15)
        
        # 创建恢复记录
        self.create_recovery = QCheckBox("创建恢复记录")
        self.create_recovery.setStyleSheet(checkbox_style)
        row3.addWidget(self.create_recovery)
        
        # 测试压缩文件 - 移到第三行，默认勾选
        self.test_archive = QCheckBox("测试压缩文件")
        self.test_archive.setChecked(True)  # 设置为默认勾选
        self.test_archive.setStyleSheet(checkbox_style)
        row3.addWidget(self.test_archive)
        
        # 移除自解压格式选项
        
        row3.addStretch(1)
        
        # 添加第三行到主布局
        advanced_layout.addLayout(row3)
        
        # 添加自动刷新变量但不显示在界面上
        self.auto_refresh = QCheckBox("自动刷新")
        self.auto_refresh.setVisible(False)
        self.auto_refresh.setChecked(False)
        
        # 连接信号
        self.enable_split.stateChanged.connect(self.toggle_split_count)
        self.auto_refresh.stateChanged.connect(self.toggle_auto_refresh)
        
        # 添加到设置布局
        settings_layout.addWidget(advanced_group)
        
        # 解压密码提示
        password_info = QWidget()
        password_info.setStyleSheet("""
            QWidget {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
            }
        """)
        
        # 设置解压密码区域的最小高度 - 减小高度
        password_info.setMinimumHeight(60)
        
        # 创建主布局
        main_layout = QVBoxLayout(password_info)
        main_layout.setContentsMargins(10, 8, 10, 8)  # 减小内边距
        main_layout.setSpacing(4)  # 减小间距
        
        # 创建密码显示容器
        password_container = QWidget()
        password_container.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
                border-radius: 8px;
            }
        """)
        
        password_layout = QHBoxLayout(password_container)
        password_layout.setContentsMargins(10, 10, 10, 10)
        password_layout.setSpacing(10)
        password_layout.setAlignment(Qt.AlignCenter)
        
        # 创建解压密码文本标签
        title_label = QLabel("解压密码")
        title_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 14px;
                font-weight: 400;
                font-family: 'Microsoft YaHei UI';
                background: transparent;
                padding-right: 5px;
            }
        """)
        
        # 创建密码值容器 - 更醒目的设计
        password_value_container = QWidget()
        password_value_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff9a9e,
                    stop:1 #fad0c4);
                border: none;
                border-radius: 6px;
            }
        """)
        
        # 添加多层阴影效果
        box_shadow = QGraphicsDropShadowEffect()
        box_shadow.setBlurRadius(20)
        box_shadow.setColor(QColor(255, 154, 158, 160))
        box_shadow.setOffset(0, 4)
        password_value_container.setGraphicsEffect(box_shadow)
        
        # 创建密码值标签
        password_value_layout = QHBoxLayout(password_value_container)
        password_value_layout.setContentsMargins(15, 7, 15, 7)  # 调整内边距
        password_value_layout.setSpacing(0)
        
        password_label = QLabel("2048论坛")
        password_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: 800;
                letter-spacing: 1.2px;
                font-family: 'Microsoft YaHei UI';
                text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3),
                             0px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        password_value_layout.addWidget(password_label)
        
        # 添加标签到主布局
        password_layout.addWidget(title_label)
        password_layout.addWidget(password_value_container)
        
        main_layout.addWidget(password_container)
        
        settings_layout.addWidget(password_info)
        
        # 添加按钮区域
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(6)  # 设置按钮之间有轻微间距
        
        # 所有按钮的通用样式
        button_style = """
            font-family: 'Microsoft YaHei UI';
            font-size: 13px;
            font-weight: 500;
            background-color: rgba(248, 250, 252, 0.7);
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 8px;
            color: #64748b;
            padding: 8px 16px;
        """
        
        button_hover_style = """
            background-color: rgba(241, 245, 249, 0.9);
            border: 1px solid rgba(203, 213, 225, 0.9);
            color: #475569;
        """
        
        button_pressed_style = """
            background-color: rgba(226, 232, 240, 0.9);
            border: 1px solid rgba(148, 163, 184, 0.9);
            color: #334155;
            padding-top: 9px;
            padding-bottom: 7px;
        """
        
        # 截图按钮
        self.pack_and_screenshot_button = QPushButton("截图")
        self.pack_and_screenshot_button.setObjectName("pack_screenshot_button")
        self.pack_and_screenshot_button.setFixedHeight(36)
        self.pack_and_screenshot_button.setStyleSheet(f"""
            QPushButton#pack_screenshot_button {{
                {button_style}
            }}
            QPushButton#pack_screenshot_button:hover {{
                {button_hover_style}
            }}
            QPushButton#pack_screenshot_button:pressed {{
                {button_pressed_style}
            }}
        """)
        
        # 添加按钮阴影效果
        screenshot_shadow = QGraphicsDropShadowEffect()
        screenshot_shadow.setBlurRadius(10)
        screenshot_shadow.setColor(QColor(100, 116, 139, 60))
        screenshot_shadow.setOffset(0, 2)
        self.pack_and_screenshot_button.setGraphicsEffect(screenshot_shadow)
        
        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setFixedHeight(36)
        self.cancel_button.setStyleSheet(f"""
            QPushButton {{
                {button_style}
            }}
            QPushButton:hover {{
                {button_hover_style}
            }}
            QPushButton:pressed {{
                {button_pressed_style}
            }}
        """)
        
        # 添加按钮阴影效果
        cancel_shadow = QGraphicsDropShadowEffect()
        cancel_shadow.setBlurRadius(10)
        cancel_shadow.setColor(QColor(100, 116, 139, 60))
        cancel_shadow.setOffset(0, 2)
        self.cancel_button.setGraphicsEffect(cancel_shadow)
        
        # 开始按钮
        self.ok_button = QPushButton("开始")
        self.ok_button.setObjectName("main_button")
        self.ok_button.setFixedHeight(36)
        self.ok_button.setStyleSheet(f"""
            QPushButton#main_button {{
                {button_style}
            }}
            QPushButton#main_button:hover {{
                {button_hover_style}
            }}
            QPushButton#main_button:pressed {{
                {button_pressed_style}
            }}
        """)
        
        # 添加按钮阴影效果
        ok_shadow = QGraphicsDropShadowEffect()
        ok_shadow.setBlurRadius(10)
        ok_shadow.setColor(QColor(100, 116, 139, 60))
        ok_shadow.setOffset(0, 2)
        self.ok_button.setGraphicsEffect(ok_shadow)
        
        # 设置按钮的最小宽度，允许自适应
        self.pack_and_screenshot_button.setMinimumWidth(80)
        self.cancel_button.setMinimumWidth(80)
        self.ok_button.setMinimumWidth(80)
        
        # 创建水平布局，均匀分布按钮
        button_layout.addWidget(self.pack_and_screenshot_button, 1)  # 权重为1
        button_layout.addWidget(self.cancel_button, 1)               # 权重为1
        button_layout.addWidget(self.ok_button, 1)                   # 权重为1
        
        settings_layout.addLayout(button_layout)
        
        right_layout.addWidget(settings_group)
        return right_panel
    
    def create_app_icon(self):
        """创建应用图标"""
        # 创建一个28x28的图标
        icon = QPixmap(28, 28)
        icon.fill(Qt.transparent)
        
        painter = QPainter(icon)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 创建主背景渐变
        gradient = QLinearGradient(0, 0, 28, 28)
        gradient.setColorAt(0, QColor("#4338ca"))  # 深蓝色起始
        gradient.setColorAt(1, QColor("#3b82f6"))  # 亮蓝色结束
        
        # 绘制圆形背景
        painter.setBrush(gradient)
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(0, 0, 28, 28)
        
        # 绘制压缩文件图标（白色）
        painter.setPen(QPen(QColor("#ffffff"), 2))
        
        # 绘制文件夹形状
        folder_path = QPainterPath()
        folder_path.moveTo(7, 8)
        folder_path.lineTo(12, 8)
        folder_path.lineTo(14, 10)
        folder_path.lineTo(21, 10)
        folder_path.lineTo(21, 20)
        folder_path.lineTo(7, 20)
        folder_path.closeSubpath()
        
        # 使用半透明白色填充文件夹
        painter.setBrush(QColor(255, 255, 255, 100))
        painter.drawPath(folder_path)
        
        # 绘制压缩箭头
        painter.setPen(QPen(QColor("#ffffff"), 2))
        # 向内的箭头
        painter.drawLine(11, 13, 14, 16)
        painter.drawLine(17, 13, 14, 16)
        # 向外的箭头
        painter.drawLine(11, 17, 14, 14)
        painter.drawLine(17, 17, 14, 14)
        
        painter.end()
        return icon

    def restore_window_position(self):
        """恢复窗口位置"""
        settings = QSettings("2048Forum", "FolderViewer")
        saved_pos = settings.value("compress_dialog_pos")
        if saved_pos:
            screen = QApplication.primaryScreen().geometry()
            pos_x = max(0, min(saved_pos.x(), screen.width() - self.width()))
            pos_y = max(0, min(saved_pos.y(), screen.height() - self.height()))
            self.move(pos_x, pos_y)
        else:
            self.center_on_parent()

    def center_on_parent(self):
        """将对话框居中显示在父窗口上"""
        if self.parent():
            parent_rect = self.parent().geometry()
            x = parent_rect.center().x() - self.width() // 2
            y = parent_rect.center().y() - self.height() // 2
            self.move(x, y)

    def mousePressEvent(self, event):
        """处理鼠标按下事件以实现窗口拖动"""
        if event.button() == Qt.LeftButton:
            # 获取鼠标相对于窗口的位置
            self._drag_pos = event.pos()
            event.accept()
    
    def mouseMoveEvent(self, event):
        """处理鼠标移动事件以实现窗口拖动"""
        if event.buttons() & Qt.LeftButton and self._drag_pos is not None:
            # 计算移动的距离
            diff = event.pos() - self._drag_pos
            # 移动窗口
            new_pos = self.pos() + diff
            self.move(new_pos)
            event.accept()
    
    def mouseReleaseEvent(self, event):
        """处理鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            # 清除拖动位置
            self._drag_pos = None
            
            # 保存新位置到注册表
            settings = QSettings("2048Forum", "FolderViewer")
            settings.setValue("compress_dialog_pos", self.pos())
            
            event.accept()

    def dragEnterEvent(self, event):
        """处理拖入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
        else:
            event.ignore()
    
    def dropEvent(self, event):
        """处理拖放事件"""
        event.accept()
        urls = event.mimeData().urls()
        if urls:
            folder_path = urls[0].toLocalFile()
            if os.path.isdir(folder_path):
                # 更新主窗口
                if self.parent_window:
                    self.parent_window.open_folder(folder_path)
                # 更新压缩对话框
                self.update_files_list(folder_path)
                folder_name = os.path.basename(folder_path)
                default_output = os.path.join(os.path.dirname(folder_path), f"{folder_name}.rar")
                self.output_path.setText(default_output)

    def toggle_split_count(self, state):
        """切换分卷数量输入框的启用状态"""
        self.split_count.setEnabled(state == Qt.Checked)

    def toggle_auto_refresh(self, state):
        """切换自动刷新状态"""
        if state == Qt.Checked:
            self.refresh_timer.start()
        else:
            self.refresh_timer.stop()

    def browse_output_path(self):
        """浏览并选择输出路径"""
        # 获取当前选择的格式
        if self.rar_radio.isChecked():
            current_format = "rar"
        elif self.rar4_radio.isChecked():
            current_format = "rar"  # RAR4仍使用.rar扩展名
        elif self.zip_radio.isChecked():
            current_format = "zip"
        else:
            current_format = "rar"  # 默认使用RAR
        
        if self.current_folder:
            initial_dir = os.path.dirname(self.current_folder)
            default_name = os.path.basename(self.current_folder) + f".{current_format}"
        else:
            initial_dir = os.path.expanduser("~")
            default_name = f"compressed.{current_format}"
            
        # 根据选择的格式设置过滤器
        if current_format == "rar":
            filter_str = "RAR文件 (*.rar)"
        elif current_format == "zip":
            filter_str = "ZIP文件 (*.zip)"
        else:
            filter_str = "压缩文件 (*.rar *.zip)"
            
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "选择保存位置",
            os.path.join(initial_dir, default_name),
            filter_str
        )
        
        if file_path:
            self.output_path.setText(file_path)

    def start_compression(self):
        """开始压缩处理"""
        if not self.current_folder:
            QMessageBox.warning(self, "警告", "请先选择要压缩的文件夹")
            return
            
        if not self.output_path.text():
            QMessageBox.warning(self, "警告", "请选择保存位置")
            return
        
        try:
            # 获取分卷设置
            use_split = self.enable_split.isChecked()
            if use_split:
                try:
                    split_count = int(self.split_count.text())
                    if split_count < 1:
                        QMessageBox.warning(self, "警告", "分卷数量必须大于0")
                        return
                except ValueError:
                    QMessageBox.warning(self, "警告", "请输入有效的分卷数量")
                    return
            
            # 获取压缩设置
            output_path = self.output_path.text()
            compression_level = self.compression_level.currentText()
            use_password = self.use_password.isChecked()
            password = self.password.text() if use_password else None
            
            # 获取新增的设置 - 使用单选按钮组
            if self.rar_radio.isChecked():
                archive_format = "rar"
            elif self.rar4_radio.isChecked():
                archive_format = "rar4"
            elif self.zip_radio.isChecked():
                archive_format = "zip"
            else:
                archive_format = "rar"  # 默认使用RAR
                
            test_archive = self.test_archive.isChecked()
            create_recovery = self.create_recovery.isChecked()
            create_sfx = False  # 自解压选项已移除
            
            # 确保输出文件扩展名与所选格式匹配
            if not output_path.lower().endswith(f".{archive_format}"):
                output_path = os.path.splitext(output_path)[0] + f".{archive_format}"
                self.output_path.setText(output_path)
            
            # 设置压缩级别参数
            level_params = {
                "存储": "-m0",
                "最快": "-m1",
                "快速": "-m2",
                "标准": "-m3",
                "最大": "-m5"
            }
            level_param = level_params.get(compression_level, "-m3")
            
            # 构建压缩命令
            cmd_path = self.parent_window.rar_path
            
            rar_cmd = [
                cmd_path,
                "a",  # 添加到压缩文件
                level_param,  # 压缩级别
            ]
            
            # 如果是RAR4格式，添加特定参数
            if archive_format == "rar4":
                rar_cmd.append("-ma4")  # 使用RAR4格式
            
            # 添加测试参数
            if test_archive:
                rar_cmd.append("-t")  # 压缩后测试
                
            # 添加恢复记录参数（RAR和RAR4格式支持）
            if create_recovery and (archive_format == "rar" or archive_format == "rar4"):
                rar_cmd.append("-rr3")  # 添加3%的恢复记录
                
            # 添加自解压参数（RAR和RAR4格式支持）
            if create_sfx and (archive_format == "rar" or archive_format == "rar4"):
                rar_cmd.append("-sfx")  # 创建自解压格式
            
            # 添加分卷参数
            if use_split:
                rar_cmd.extend([f"-v{split_count}"])
            
            rar_cmd.extend([
                "-r",  # 递归子目录
            ])
            
            # 添加密码参数
            if use_password and password:
                rar_cmd.extend([f"-p{password}"])
            
            # 添加输出路径和源文件夹
            rar_cmd.extend([output_path, self.current_folder])
            
            # 更新UI状态
            self.progress_bar.setValue(0)
            self.progress_status.setText("正在压缩...")
            self.progress_percent.setText("0%")
            
            # 创建进程
            process = subprocess.Popen(
                rar_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            # 创建定时器来更新进度
            self.compression_timer = QTimer()
            self.compression_timer.timeout.connect(lambda: self.update_compression_progress(process))
            self.compression_timer.start(100)  # 每100ms更新一次
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"压缩过程中出错：{str(e)}")
            self.progress_status.setText("压缩失败")
            self.progress_percent.setText("0%")
            self.progress_bar.setValue(0)
    
    def update_compression_progress(self, process):
        """更新压缩进度"""
        try:
            # 检查进程是否完成
            if process.poll() is not None:
                self.compression_timer.stop()
                
                # 检查是否成功完成
                if process.returncode == 0:
                    self.progress_bar.setValue(100)
                    self.progress_status.setText("压缩完成")
                    self.progress_percent.setText("100%")
                    QMessageBox.information(self, "完成", "文件压缩完成")
                else:
                    stderr = process.stderr.read().decode('gbk', errors='ignore')
                    self.progress_status.setText("压缩失败")
                    self.progress_percent.setText("0%")
                    QMessageBox.warning(self, "错误", f"压缩失败：{stderr}")
                return
            
            # 获取输出文件大小作为进度指示
            output_base = os.path.splitext(self.output_path.text())[0]
            total_size = 0
            part_num = 1
            
            while True:
                part_path = f"{output_base}.part{part_num}.rar"
                if not os.path.exists(part_path):
                    break
                total_size += os.path.getsize(part_path)
                part_num += 1
            
            # 估算进度（基于输出文件大小与源文件夹大小的比例）
            source_size = sum(
                os.path.getsize(os.path.join(root, file))
                for root, _, files in os.walk(self.current_folder)
                for file in files
            )
            
            if source_size > 0:
                progress = min(95, int((total_size / source_size) * 100))
                self.progress_bar.setValue(progress)
                self.progress_percent.setText(f"{progress}%")
        
        except Exception as e:
            print(f"更新进度出错：{str(e)}")
            self.progress_status.setText("更新进度出错")
            self.progress_percent.setText("0%")

class SimpleFolderViewer(QMainWindow):
    def __init__(self):
        try:
            logger.info("初始化 SimpleFolderViewer...")
            super().__init__()
            
            # 检查ffmpeg
            self.check_ffmpeg()
            
            # 隐藏窗口标题栏左侧的系统菜单图标
            self.setWindowFlags(self.windowFlags() & ~Qt.WindowSystemMenuHint)
            
            # 设置窗口样式 - 明亮主题
            self.setStyleSheet("""
                QMainWindow {
                    background: #ffffff;
                    color: #333333;
                }
                QMainWindow::title {
                    background: #f0f4f8;
                    color: #1f2937;
                    padding: 0px;
                    height: 38px;
                    border-bottom: 1px solid #e5e7eb;
                }
                QMenuBar {
                    background: #f0f4f8;
                    color: #1f2937;
                    font-family: 'Microsoft YaHei UI';
                    font-size: 13px;
                    font-weight: 550;
                    padding: 2px;
                    border: none;
                    border-bottom: 1px solid #e5e7eb;
                }
                QMenuBar::item {
                    background: transparent;
                    padding: 6px 10px;
                    margin: 0px;
                    border-radius: 4px;
                }
                QMenuBar::item:selected {
                    background: rgba(59, 130, 246, 0.1);
                    border-radius: 4px;
                }
                QMenuBar::item:pressed {
                    background: rgba(59, 130, 246, 0.2);
                    border-radius: 4px;
                }
                QToolBar {
                    background: #f0f4f8;
                    border: none;
                    border-bottom: 1px solid #e5e7eb;
                    padding: 6px;
                    spacing: 12px;
                    alignment: left;
                }
                QToolBar::handle {
                    image: none;
                    width: 0px;
                    height: 0px;
                    padding: 0px;
                    margin: 0px;
                }
                QStatusBar {
                    background: #f8fafc;
                    border-top: 1px solid #e5e7eb;
                    color: #1f2937;
                    font-weight: 500;
                    padding: 3px 10px;
                }
                QSplitter::handle {
                    background: #e5e7eb;
                    border: none;
                    margin: 1px;
                    width: 1px;
                }
                QSplitter::handle:hover {
                    background: #d1d5db;
                }
                QSplitter::handle:pressed {
                    background: #9ca3af;
                }
                QLabel {
                    color: #1f2937;
                    font-family: 'Microsoft YaHei UI';
                    font-size: 14px;
                }
            """)
            
            logger.info("初始化UI...")
            self.initUI()
            self.current_folder = None
            self.rar_path = None
            self.explorer = None  # 初始化explorer为None
            
            # 安装事件过滤器，监听窗口激活事件
            self.installEventFilter(self)
            
            # 恢复窗口状态
            self.restore_window_state()
            
            # 添加状态栏
            self.statusBar().setStyleSheet("""
                QStatusBar {
                    background: #f8fafc;
                    border-top: 1px solid #e2e8f0;
                    padding: 4px 12px;
                    font-family: 'Microsoft YaHei UI';
                    font-size: 13px;
                    color: #1a237e;
                }
                
                QStatusBar::item {
                    border: none;
                }
            """)
            
            logger.info("SimpleFolderViewer 初始化完成")
        
        except Exception as e:
            logger.critical(f"SimpleFolderViewer 初始化失败: {str(e)}", exc_info=True)
            QMessageBox.critical(None, "初始化错误", 
                               f"程序初始化失败，请检查日志文件获取更多信息。\n\n错误: {str(e)}")
            raise

    def ensure_style_applied(self):
        """确保文件浏览器保持正确的样式"""
        pass

    def initUI(self):
        # 设置窗口标题和最小大小
        self.setWindowTitle('文件管理器')
        self.setMinimumSize(1000, 700)
        
        # 设置窗口样式 - 明亮主题
        self.setStyleSheet("""
            QMainWindow {
                background: #ffffff;
                color: #333333;
            }
            QMainWindow::title {
                /* 使用浅色系立体效果，通过边框和阴影创造深度 */
                background: #f0f5ff;
                color: #333333;
                padding: 0px;
                height: 38px;
                border-top: 1px solid #ffffff;
                border-left: 1px solid #ffffff;
                border-right: 1px solid #e0e5f5;
                border-bottom: 1px solid #d0d8f0;
                box-shadow: inset 0px 1px 0px #ffffff, 
                            inset -1px 0px 0px #f8faff,
                            0px 2px 4px rgba(0, 0, 0, 0.05);
            }
            QMenuBar {
                /* 浅色系立体效果，通过细微的内阴影和高光创造立体感 */
                background: #f5f8ff;
                color: #333333;
                font-family: 'Microsoft YaHei UI';
                font-size: 13px;
                font-weight: 600;
                padding: 4px;
                border-top: 1px solid #ffffff;
                border-bottom: 1px solid #e0e5f5;
                box-shadow: inset 0px 1px 0px #ffffff,
                            0px 1px 2px rgba(0, 0, 0, 0.03);
            }
            QMenuBar::item {
                background: transparent;
                padding: 6px 12px;
                margin: 1px 2px;
                border-radius: 5px;
            }
            QMenuBar::item:selected {
                /* 选中项的立体效果 */
                background: #e8f0ff;
                border-radius: 5px;
                border: 1px solid #d5e0f5;
                box-shadow: inset 0px 1px 0px #ffffff,
                            inset 0px -1px 0px #f0f5ff;
            }
            QMenuBar::item:pressed {
                /* 按下时的凹陷效果 */
                background: #d8e5ff;
                border-radius: 5px;
                border: 1px solid #c0d0f0;
                box-shadow: inset 0px 1px 2px rgba(0, 0, 0, 0.05);
                padding-top: 7px;
                padding-bottom: 5px;
            }
            QToolBar {
                /* 工具栏的立体效果 */
                background: #f0f5ff;
                border-top: 1px solid #ffffff;
                border-bottom: 1px solid #d8e0f5;
                box-shadow: inset 0px 1px 0px #ffffff,
                            0px 1px 3px rgba(0, 0, 0, 0.03);
                padding: 8px;
                spacing: 12px;
                alignment: left;
            }
            QToolBar::handle {
                image: none;
                width: 0px;
                height: 0px;
                padding: 0px;
                margin: 0px;
            }
            QStatusBar {
                background: #f8faff;
                border-top: 1px solid #e5e9f5;
                color: #1f2937;
                font-weight: 500;
                padding: 3px 10px;
            }
            QSplitter::handle {
                background: #e5e9f5;
                border: none;
                margin: 1px;
                width: 1px;
            }
            QSplitter::handle:hover {
                background: #d0d8f0;
            }
            QSplitter::handle:pressed {
                background: #b8c5e8;
            }
            QLabel {
                color: #1f2937;
                font-family: 'Microsoft YaHei UI';
                font-size: 14px;
            }
        """)
        
        # 创建工具栏 - 只使用顶部标题，不再放置按钮
        self.toolbar = QToolBar("标题栏")
        self.toolbar.setIconSize(QSize(16, 16))
        self.toolbar.setMovable(False)
        self.toolbar.setFixedHeight(40)  # 减小标题栏高度
        self.addToolBar(Qt.TopToolBarArea, self.toolbar)
        
        # 添加左侧少量间距
        spacer = QWidget()
        spacer.setFixedWidth(15)
        self.toolbar.addWidget(spacer)
        
        # 创建APP图标 - 水晶钻石风格
        app_icon = QLabel()
        icon_pixmap = QPixmap(36, 36)  # 减小图标尺寸
        icon_pixmap.fill(Qt.transparent)
        painter = QPainter(icon_pixmap)
        painter.setRenderHint(QPainter.Antialiasing, True)
        
        # 导入数学库用于绘制几何形状
        import math
        
        # 创建豪华水晶钻石效果的背景
        # 定义钻石的形状点
        centerX, centerY = 18, 18
        radius = 15
        points = []
        
        # 创建一个8边形钻石形状
        for i in range(8):
            angle = (i * 45 + 22.5) * math.pi / 180
            x = centerX + radius * math.cos(angle)
            y = centerY + radius * math.sin(angle)
            points.append(QPointF(x, y))
        
        # 创建路径
        diamondPath = QPainterPath()
        diamondPath.moveTo(points[0])
        for i in range(1, 8):
            diamondPath.lineTo(points[i])
        diamondPath.closeSubpath()
        
        # 创建天空蓝色渐变
        diamondGradient = QLinearGradient(0, 0, 42, 42)
        diamondGradient.setColorAt(0.0, QColor("#3d7fff"))    # 亮蓝色
        diamondGradient.setColorAt(0.25, QColor("#85b8ff"))   # 浅蓝色
        diamondGradient.setColorAt(0.5, QColor("#aad0ff"))    # 更浅的蓝色
        diamondGradient.setColorAt(0.75, QColor("#85b8ff"))   # 浅蓝色
        diamondGradient.setColorAt(1.0, QColor("#3d7fff"))    # 亮蓝色
        
        # 绘制钻石主体
        painter.setBrush(diamondGradient)
        painter.setPen(QPen(QColor(255, 255, 255, 180), 1.2))
        painter.drawPath(diamondPath)
        
        # 添加内部发光效果 - 蓝色系
        innerGlow = QRadialGradient(centerX-4, centerY-4, radius*0.8)
        innerGlow.setColorAt(0, QColor(220, 240, 255, 180))
        innerGlow.setColorAt(0.5, QColor(200, 230, 255, 50))
        innerGlow.setColorAt(1, QColor(180, 220, 255, 0))
        
        painter.setBrush(innerGlow)
        painter.setPen(Qt.NoPen)
        painter.drawPath(diamondPath)
        
        # 添加钻石切面效果
        # 上半部分
        facetPath1 = QPainterPath()
        facetPath1.moveTo(centerX, centerY-radius*0.6)
        facetPath1.lineTo(centerX+radius*0.6, centerY)
        facetPath1.lineTo(centerX, centerY+radius*0.6)
        facetPath1.lineTo(centerX-radius*0.6, centerY)
        facetPath1.closeSubpath()
        
        facetGradient1 = QLinearGradient(centerX-radius*0.6, centerY-radius*0.6, centerX+radius*0.6, centerY+radius*0.6)
        facetGradient1.setColorAt(0, QColor(255, 255, 255, 200))
        facetGradient1.setColorAt(1, QColor(200, 220, 255, 100))
        
        painter.setBrush(facetGradient1)
        painter.setPen(QPen(QColor(255, 255, 255, 120), 0.8))
        painter.drawPath(facetPath1)
        
        # 添加反光和闪光点
        painter.setBrush(QColor(255, 255, 255, 230))
        painter.drawEllipse(QPointF(centerX-radius*0.3, centerY-radius*0.3), 2, 2)
        painter.drawEllipse(QPointF(centerX+radius*0.4, centerY-radius*0.2), 1.5, 1.5)
        painter.drawEllipse(QPointF(centerX-radius*0.1, centerY+radius*0.4), 1, 1)
        
        # 绘制小型文件夹图标在钻石中心
        folderIcon = QPainterPath()
        folderIcon.moveTo(centerX-6, centerY-1)
        folderIcon.lineTo(centerX-3, centerY-1)
        folderIcon.lineTo(centerX-1, centerY+1)
        folderIcon.lineTo(centerX+6, centerY+1)
        folderIcon.lineTo(centerX+5, centerY+7)
        folderIcon.lineTo(centerX-5, centerY+7)
        folderIcon.closeSubpath()
        
        # 文件夹顶部
        tabPath = QPainterPath()
        tabPath.moveTo(centerX-3, centerY-1)
        tabPath.lineTo(centerX-3, centerY-3)
        tabPath.lineTo(centerX, centerY-3)
        tabPath.lineTo(centerX, centerY-1)
        
        # 绘制白色半透明文件夹
        painter.setBrush(QColor(255, 255, 255, 210))
        painter.setPen(QPen(QColor(255, 255, 255, 250), 1.2))
        painter.drawPath(folderIcon)
        painter.drawPath(tabPath)
        
        painter.end()
        
        app_icon.setPixmap(icon_pixmap)
        app_icon.setFixedSize(36, 36)
        
        # 首先移除所有现有的工具栏内容
        while self.toolbar.actions():
            self.toolbar.removeAction(self.toolbar.actions()[0])
            
        # 添加左侧空间
        spacer_left = QWidget()
        spacer_left.setFixedWidth(10)
        self.toolbar.addWidget(spacer_left)
        
        # 使用完全不同的方法 - 使用绝对定位
        icon_wrapper = QWidget()
        icon_wrapper.setFixedSize(46, 40)
        
        # 关键部分：使用绝对定位手动调整位置
        app_icon.setParent(icon_wrapper)
        app_icon.move(5, -2)  # 第二个参数是上下位置，值越小图标越往上，值越大图标越往下
        
        # 添加这个自定义容器到工具栏
        action = self.toolbar.addWidget(icon_wrapper)
        
        # 设置工具栏全局样式
        self.toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # 为图标添加更华丽的蓝色阴影效果
        icon_shadow = QGraphicsDropShadowEffect()
        icon_shadow.setBlurRadius(18)
        icon_shadow.setColor(QColor(70, 130, 220, 90))  # 蓝色阴影
        icon_shadow.setOffset(1, 2)
        app_icon.setGraphicsEffect(icon_shadow)
        
        # 应用名称 - 使用黑色文字+蓝色阴影的3D效果
        app_title = QLabel("文件管理器")
        app_title.setStyleSheet("""
            font-size: 18px;
            font-weight: 800;
            color: #1a1a1a;
            padding-left: 10px;
            letter-spacing: 1.5px;
            text-shadow: 0px 1px 0px #b0d0ff,
                         0px 2px 0px #80b0e0,
                         0px 3px 0px #5090c0,
                         0px 4px 0px #3070a0,
                         0px 5px 5px rgba(0, 0, 0, 0.35),
                         1px 1px 2px rgba(255, 255, 255, 0.7),
                         -1px -1px 1px rgba(0, 60, 120, 0.25);
            font-family: 'Microsoft YaHei UI';
        """)
        self.toolbar.addWidget(app_title)
        
        # 添加弹性空间
        stretch1 = QWidget()
        stretch1.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.toolbar.addWidget(stretch1)
        
        # 添加水晶玻璃质感按钮样式 - 保留定义供后续使用
        btn_base_style = """
            QPushButton {{
                color: #334155;
                border: 1px solid rgba(255, 255, 255, 0.8);
                border-right: 1px solid rgba(200, 210, 230, 0.8);
                border-bottom: 1px solid rgba(200, 210, 230, 0.8);
                border-radius: 10px;
                font-size: 13px;
                font-weight: 600;
                padding: 8px 24px;
                font-family: 'Microsoft YaHei UI';
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #ffffff, 
                    stop:0.3 {light}, 
                    stop:1 {main});
                min-width: 85px;
            }}
            QPushButton:hover {{
                color: #1e293b;
                border: 1px solid rgba(255, 255, 255, 0.9);
                border-right: 1px solid rgba(180, 190, 210, 0.9);
                border-bottom: 1px solid rgba(180, 190, 210, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #ffffff, 
                    stop:0.5 #ffffff,
                    stop:1 {lighter});
            }}
            QPushButton:pressed {{
                padding-top: 9px;
                padding-bottom: 7px;
                color: #0f172a;
                border: 1px solid rgba(190, 200, 220, 0.9);
                border-top: 1px solid rgba(160, 170, 190, 0.9);
                border-left: 1px solid rgba(160, 170, 190, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 {main}, 
                    stop:0.4 {light},
                    stop:1 #ffffff);
            }}
        """
        
        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局
        self.main_layout = QHBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(10, 10, 10, 10)
        self.main_layout.setSpacing(10)
        
        # 设置中央窗口背景颜色
        self.central_widget.setStyleSheet("""
            QWidget {
                background: #f8fafc;
            }
        """)
        
        # 创建分隔器
        self.splitter = QSplitter(Qt.Horizontal)
        self.splitter.setHandleWidth(1)
        self.splitter.setChildrenCollapsible(False)
        
        # 添加自定义的事件处理以确保分隔器的调整传递给子控件
        def splitter_resize_event(event):
            QSplitter.resizeEvent(self.splitter, event)
            # 调整子控件大小并刷新界面
            self.splitter.setSizes(self.splitter.sizes())
            for i in range(self.splitter.count()):
                widget = self.splitter.widget(i)
                if widget:
                    widget.updateGeometry()
                    widget.update()
            QApplication.processEvents()
        
        self.splitter.resizeEvent = splitter_resize_event
        
        self.main_layout.addWidget(self.splitter)
        
        # 创建左侧容器
        self.explorer_container = QFrame()
        self.explorer_container.setMinimumWidth(200)
        self.explorer_container.setFrameShape(QFrame.StyledPanel)
        self.explorer_container.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border-radius: 8px;
                border: 1px solid #e5e7eb;
            }
        """)
        
        # 添加阴影效果
        explorer_shadow = QGraphicsDropShadowEffect()
        explorer_shadow.setBlurRadius(20)
        explorer_shadow.setColor(QColor(0, 0, 0, 30))
        explorer_shadow.setOffset(0, 0)
        self.explorer_container.setGraphicsEffect(explorer_shadow)
        
        self.explorer_layout = QVBoxLayout(self.explorer_container)
        self.explorer_layout.setContentsMargins(0, 0, 0, 0)
        self.explorer_layout.setSpacing(0)
        
        # 创建顶部工具栏 - 按钮靠左显示
        top_buttons_container = QWidget()
        top_buttons_container.setFixedHeight(50)  # 限制高度
        top_buttons_layout = QHBoxLayout(top_buttons_container)
        top_buttons_layout.setContentsMargins(5, 5, 5, 5)
        top_buttons_layout.setSpacing(4)  # 减小间距
        top_buttons_layout.setAlignment(Qt.AlignLeft)  # 按钮强制靠左对齐
        
        # 设置按钮
        view_btn = QPushButton("设置")
        view_btn.setCursor(Qt.PointingHandCursor)
        view_btn.setFixedHeight(36)
        
        # 使用双层阴影效果增强立体质感
        view_btn_shadow = QGraphicsDropShadowEffect()
        view_btn_shadow.setBlurRadius(15)
        view_btn_shadow.setColor(QColor(120, 150, 220, 60))
        view_btn_shadow.setOffset(0, 3)
        view_btn.setGraphicsEffect(view_btn_shadow)
        
        view_btn.setStyleSheet(btn_base_style.format(
            light="#e6efff", 
            main="#c3d4fd",
            lighter="#f0f7ff"))
        
        view_btn.setToolTip("设置")
        view_btn.clicked.connect(lambda: self.take_screenshot(False))
        top_buttons_layout.addWidget(view_btn)
        
        # 文件按钮
        password_btn = QPushButton("文件")
        password_btn.setCursor(Qt.PointingHandCursor)
        password_btn.setFixedHeight(36)
        
        # 文件按钮阴影
        pass_btn_shadow = QGraphicsDropShadowEffect()
        pass_btn_shadow.setBlurRadius(15)
        pass_btn_shadow.setColor(QColor(100, 180, 120, 60))
        pass_btn_shadow.setOffset(0, 3)
        password_btn.setGraphicsEffect(pass_btn_shadow)
        
        password_btn.setStyleSheet(btn_base_style.format(
            light="#e8f5e9", 
            main="#c0e5c1",
            lighter="#f1f8f2"))
        
        password_btn.setToolTip("文件")
        password_btn.clicked.connect(lambda: self.take_screenshot(True))
        top_buttons_layout.addWidget(password_btn)
        
        # 动图制作按钮
        settings_btn = QPushButton("动图制作")  # 保留原始文字
        settings_btn.setCursor(Qt.PointingHandCursor)
        settings_btn.setFixedHeight(36)
        
        # 动图制作按钮阴影
        settings_btn_shadow = QGraphicsDropShadowEffect()
        settings_btn_shadow.setBlurRadius(15)
        settings_btn_shadow.setColor(QColor(170, 130, 200, 60))
        settings_btn_shadow.setOffset(0, 3)
        settings_btn.setGraphicsEffect(settings_btn_shadow)
        
        settings_btn.setStyleSheet(btn_base_style.format(
            light="#f3e5f5", 
            main="#d4b6e1",
            lighter="#f7ecf9"))
        
        settings_btn.setToolTip("压缩当前文件夹")
        settings_btn.clicked.connect(self.compress_folder)
        top_buttons_layout.addWidget(settings_btn)
        
        # 视频预览图按钮
        preview_btn = QPushButton("视频预览图")  # 保留原始文字
        preview_btn.setCursor(Qt.PointingHandCursor)
        preview_btn.setFixedHeight(36)
        
        # 视频预览图按钮阴影
        preview_btn_shadow = QGraphicsDropShadowEffect()
        preview_btn_shadow.setBlurRadius(15)
        preview_btn_shadow.setColor(QColor(220, 120, 120, 60))
        preview_btn_shadow.setOffset(0, 3)
        preview_btn.setGraphicsEffect(preview_btn_shadow)
        
        preview_btn.setStyleSheet(btn_base_style.format(
            light="#ffebee", 
            main="#ffbdc5",
            lighter="#fff0f1"))
        
        preview_btn.setToolTip("仅装饰按钮")
        top_buttons_layout.addWidget(preview_btn)
        
        # 添加按钮容器到左侧布局
        self.explorer_layout.addWidget(top_buttons_container)
        
        # 创建提示标签
        self.label = QLabel('将文件夹拖放到右侧区域查看')
        self.label.setAlignment(Qt.AlignCenter)
        self.label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei UI';
                font-size: 15px;
                color: #3b82f6;
                background: transparent;
                padding: 40px;
                border-radius: 8px;
                border: 2px dashed #e5e7eb;
                margin: 20px;
            }
            
            QLabel:hover {
                color: #1d4ed8;
                border-color: #93c5fd;
            }
        """)
        self.explorer_layout.addWidget(self.label)
        
        # 右侧拖放区域
        self.drop_area = DropArea(self)
        self.drop_area.setMinimumWidth(100)
        self.drop_area.setStyleSheet("""
            DropArea {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                margin: 0px;
            }
            
            DropArea:hover {
                background: #f9fafb;
                border-color: #93c5fd;
            }
            
            /* 调整内部控件样式 */
            QLabel {
                color: #3b82f6;
                font-family: 'Microsoft YaHei UI';
                font-size: 15px;
            }
            
            QTreeWidget {
                background-color: #f8fafc;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                color: #1f2937;
            }
            
            QTreeWidget::item {
                padding: 4px;
                border-radius: 4px;
            }
            
            QTreeWidget::item:selected {
                background-color: #dbeafe;
                color: #1e40af;
            }
            
            QTreeWidget::item:hover {
                background-color: #eff6ff;
            }
            
            QPushButton {
                background-color: #f0f4f8;
                color: #1f2937;
                border: 2px solid #3b82f6;
                border-radius: 6px;
                padding: 6px 12px;
            }
            
            QPushButton:hover {
                background-color: #dbeafe;
                color: #1e40af;
            }
            
            QPushButton:pressed {
                background-color: #bfdbfe;
            }
        """)
        
        # 添加阴影效果
        drop_shadow = QGraphicsDropShadowEffect()
        drop_shadow.setBlurRadius(20)
        drop_shadow.setColor(QColor(0, 0, 0, 30))
        drop_shadow.setOffset(0, 0)
        self.drop_area.setGraphicsEffect(drop_shadow)
        
        # 将左右两部分添加到分隔器
        self.splitter.addWidget(self.explorer_container)
        self.splitter.addWidget(self.drop_area)
        
        # 设置分隔器初始比例
        total_width = self.width()
        self.splitter.setSizes([int(total_width * 0.8), int(total_width * 0.2)])
        
        # 连接分隔器移动信号
        self.splitter.splitterMoved.connect(self.on_splitter_moved)
        
        # 当前的Explorer控件
        self.explorer = None

    def restore_window_state(self):
        """恢复窗口状态（大小和位置）"""
        settings = QSettings("2048Forum", "FolderViewer")
        
        # 恢复窗口几何形状
        geometry = settings.value("window_geometry")
        if geometry:
            # 确保窗口位置在当前屏幕范围内
            screen = QApplication.primaryScreen().geometry()
            if isinstance(geometry, str):
                # 如果geometry是字符串形式，需要转换
                parts = geometry.split(',')
                if len(parts) == 4:
                    x, y, w, h = map(int, parts)
                    geometry = QRect(x, y, w, h)
            
            if isinstance(geometry, QRect):
                # 确保窗口至少部分可见
                x = max(screen.left(), min(geometry.left(), screen.right() - 100))
                y = max(screen.top(), min(geometry.top(), screen.bottom() - 100))
                w = min(geometry.width(), screen.width())
                h = min(geometry.height(), screen.height())
                self.setGeometry(x, y, w, h)
        
        # 恢复分隔器状态
        splitter_state = settings.value("splitter_state")
        if splitter_state and hasattr(self, 'splitter'):
            self.splitter.restoreState(splitter_state)

    def closeEvent(self, event):
        """窗口关闭时保存状态"""
        # 保存窗口状态
        settings = QSettings("2048Forum", "FolderViewer")
        settings.setValue("window_geometry", self.geometry())
        if hasattr(self, 'splitter'):
            settings.setValue("splitter_state", self.splitter.saveState())
        
        # 释放资源
        try:
            # 关闭压缩对话框
            if hasattr(self, 'compress_dialog') and self.compress_dialog:
                self.compress_dialog.close()
                self.compress_dialog.deleteLater()
            
            # 释放Explorer控件
            if hasattr(self, 'explorer') and self.explorer:
                self.explorer_layout.removeWidget(self.explorer)
                self.explorer.deleteLater()
                self.explorer = None
            
            # 最后释放COM
            global COM_INITIALIZED
            if COM_INITIALIZED:
                try:
                    pythoncom.CoUninitialize()
                    COM_INITIALIZED = False
                except:
                    pass
        except Exception as e:
            print(f"释放资源出错: {str(e)}")
        
        # 调用父类的closeEvent
        super().closeEvent(event)

    def check_ffmpeg(self):
        """检查ffmpeg是否存在"""
        app_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        ffprobe_path = os.path.join(app_dir, 'ffprobe.exe')
        
        if not os.path.exists(ffprobe_path):
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Warning)
            msg.setWindowTitle("缺少ffmpeg")
            msg.setText("未找到ffmpeg")
            msg.setInformativeText("请将ffmpeg.exe、ffprobe.exe放在程序所在目录下。")
            msg.setStandardButtons(QMessageBox.Ok)
            
            # 设置消息框样式
            msg.setStyleSheet("""
                QMessageBox {
                    background-color: white;
                }
                QMessageBox QLabel {
                    color: #333333;
                    font-size: 12px;
                    font-family: "Microsoft YaHei UI", "Microsoft YaHei";
                }
                QPushButton {
                    background-color: #2563eb;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 5px 15px;
                    font-family: "Microsoft YaHei UI", "Microsoft YaHei";
                }
                QPushButton:hover {
                    background-color: #1d4ed8;
                }
            """)
            
            msg.exec_()
            sys.exit(1)  # 退出程序

    def open_folder(self, folder_path):
        """打开文件夹"""
        logger.info(f"尝试打开文件夹: {folder_path}")
        
        # 如果存在旧的Explorer，先移除并确保资源释放
        if self.explorer:
            try:
                logger.info("移除旧的Explorer实例")
                self.explorer_layout.removeWidget(self.explorer)
                self.explorer.deleteLater()
                self.explorer = None
                
                # 添加短暂延迟确保资源释放
                QApplication.processEvents()
                time.sleep(0.2)  # 短暂延迟
            except Exception as e:
                logger.error(f"移除旧Explorer出错: {str(e)}")

        try:
            # 显示状态消息
            self.show_status_message("正在创建资源管理器控件...")
            QApplication.processEvents()
            
            # 使用安全方法创建Explorer控件
            try:
                logger.info("创建新的CustomExplorerWidget实例")
                self.explorer = CustomExplorerWidget(self)
                logger.info("CustomExplorerWidget实例创建成功")
                
                # 检查Explorer是否成功创建
                if not self.explorer:
                    raise Exception("无法创建Explorer控件")
                    
                # 将Explorer添加到布局
                self.explorer_layout.addWidget(self.explorer)
                
                # 强制处理所有积压的事件
                QApplication.processEvents()
                
                # 隐藏标签
                self.label.hide()
                
                # 导航到文件夹
                self.show_status_message("正在加载文件夹...")
                QApplication.processEvents()
                
                success = self.navigate_to_folder(folder_path)
                
                if success:
                    self.show_status_message(f"已加载: {os.path.basename(folder_path)}")
                else:
                    self.show_status_message("加载文件夹失败")
                    
                # 强制更新布局
                self.explorer_layout.update()
                self.central_widget.updateGeometry()
                self.update()
                QApplication.processEvents()
                
                logger.info(f"文件夹 {folder_path} 打开{'成功' if success else '失败'}")
                return success
                
            except Exception as e:
                logger.error(f"创建Explorer控件失败: {str(e)}", exc_info=True)
                raise
                
        except Exception as e:
            logger.error(f"打开文件夹失败: {str(e)}", exc_info=True)
            self.label.setText(f"无法创建资源管理器控件:\n{str(e)}")
            self.label.show()
            self.show_status_message(f"打开文件夹失败: {str(e)}")
            return False
    
    def navigate_to_folder(self, folder_path):
        """导航到指定文件夹"""
        if not self.explorer:
            print("错误: Explorer 控件不存在")
            return False
            
        try:
            # 保存当前文件夹路径
            self.current_folder = folder_path
            
            # 格式化路径
            folder_path = folder_path.replace('\\', '/')
            if not folder_path.endswith('/'):
                folder_path += '/'
            
            # 创建文件URL
            file_url = f"file:///{folder_path}"
            
            # 使用重试机制进行导航
            success = False
            max_retries = 3
            
            for attempt in range(max_retries):
                try:
                    # 使用新的导航方法
                    self.explorer.navigate_to(file_url)
                    
                    # 更新窗口标题
                    self.setWindowTitle(f'文件夹查看器 - {os.path.basename(folder_path.rstrip("/"))}')
                    
                    # 设置延迟重新应用样式，确保保持渐变背景
                    QTimer.singleShot(100, lambda: self.explorer.apply_style() if self.explorer else None)
                    QTimer.singleShot(300, lambda: self.explorer.apply_style() if self.explorer else None)
                    QTimer.singleShot(600, lambda: self.explorer.apply_style() if self.explorer else None)
                    QTimer.singleShot(1000, lambda: self.explorer.apply_style() if self.explorer else None)
                    QTimer.singleShot(1500, lambda: self.explorer.apply_style() if self.explorer else None)
                    
                    success = True
                    break  # 成功后跳出重试循环
                    
                except Exception as e:
                    print(f"导航尝试 {attempt+1} 失败: {str(e)}")
                    # 短暂等待后重试
                    if attempt < max_retries - 1:
                        QApplication.processEvents()
                        time.sleep(0.5)  # 等待500ms后重试
            
            return success
        except Exception as e:
            print(f"导航错误: {str(e)}")
            return False

    def take_screenshot(self, include_whole_window=False, custom_path=None):
        """截取当前文件夹界面的截图并保存
        
        Args:
            include_whole_window: 是否包含整个应用窗口（包括对话框）
            custom_path: 自定义保存路径，如果为None则使用默认命名方式
        """
        try:
            if not self.current_folder:
                self.show_status_message("请先打开一个文件夹")
                return

            if not self.explorer:
                self.show_status_message("请先打开一个文件夹")
                return

            # 获取屏幕对象
            screen = QApplication.primaryScreen()
            
            if include_whole_window:
                # 获取左侧容器（explorer_container）和工具栏的几何信息
                container_geometry = self.explorer_container.geometry()
                toolbar_geometry = self.toolbar.geometry()
                
                # 计算需要截取的区域
                x = self.explorer_container.mapToGlobal(container_geometry.topLeft()).x()
                y = self.toolbar.mapToGlobal(toolbar_geometry.topLeft()).y()  # 从工具栏顶部开始
                width = container_geometry.width()  # 只截取左侧容器的宽度
                height = container_geometry.height() + toolbar_geometry.height()  # 包含工具栏高度
                
                # 截取指定区域
                screenshot = screen.grabWindow(
                    0,  # 0表示整个屏幕
                    x,
                    y,
                    width,
                    height
                )
            else:
                # 获取Explorer控件和工具栏的几何信息
                explorer_geometry = self.explorer.geometry()
                toolbar_geometry = self.toolbar.geometry()
                
                # 计算需要截取的区域
                x = self.explorer.mapToGlobal(explorer_geometry.topLeft()).x()
                y = self.toolbar.mapToGlobal(toolbar_geometry.topLeft()).y()  # 从工具栏顶部开始
                width = explorer_geometry.width()
                height = explorer_geometry.height() + toolbar_geometry.height()  # 包含工具栏高度
                
                # 截取指定区域
                screenshot = screen.grabWindow(
                    0,  # 0表示整个屏幕
                    x,
                    y,
                    width,
                    height
                )

            # 使用自定义路径或生成默认文件名
            if custom_path:
                filepath = custom_path
            else:
                # 生成文件名（使用文件夹名称+时间）
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                folder_name = os.path.basename(os.path.normpath(self.current_folder))
                filename = f"{folder_name}_{timestamp}.png"
                filepath = os.path.join(self.current_folder, filename)
            
            # 保存截图
            if screenshot.save(filepath):
                self.show_status_message(f"截图已保存: {os.path.basename(filepath)}")
            else:
                self.show_status_message("截图保存失败")
        
        except Exception as e:
            self.show_status_message(f"截图错误：{str(e)}")
            print(f"截图错误：{str(e)}")

    def compress_folder(self):
        """压缩当前打开的文件夹"""
        if not self.current_folder:
            QMessageBox.information(self, "压缩", "请先打开一个文件夹")
            return
        
        # 查找WinRAR路径
        rar_paths = [
            r"C:\Program Files\WinRAR\WinRAR.exe",
            r"C:\Program Files (x86)\WinRAR\WinRAR.exe",
            r"C:\Program Files\WinRAR\Rar.exe",
            r"C:\Program Files (x86)\WinRAR\Rar.exe"
        ]
        
        self.rar_path = None
        for path in rar_paths:
            if os.path.exists(path):
                self.rar_path = path
                break
        
        if not self.rar_path:
            QMessageBox.warning(self, "压缩", "找不到WinRAR程序，请确保已安装WinRAR")
            return
        
        try:
            # 创建新的压缩设置对话框
            if hasattr(self, 'compress_dialog') and self.compress_dialog:
                # 如果对话框已存在，只需更新而不是重新创建
                if self.compress_dialog.isVisible():
                    # 如果可见则只更新文件列表
                    self.compress_dialog.current_folder = self.current_folder
                    
                    # 设置默认输出文件名（使用文件夹名称）
                    folder_name = os.path.basename(os.path.normpath(self.current_folder))
                    default_output = os.path.join(os.path.dirname(self.current_folder), f"{folder_name}.rar")
                    
                    if hasattr(self.compress_dialog, 'output_path') and self.compress_dialog.output_path is not None:
                        self.compress_dialog.output_path.setText(default_output)
                    
                    # 显示状态消息
                    self.show_status_message("正在更新文件列表...")
                    
                    # 延迟更新文件列表，避免界面卡顿
                    QTimer.singleShot(50, lambda: self.compress_dialog.update_files_list(self.current_folder))
                    
                    # 视频显示区域功能已移除
                    
                    return
                else:
                    # 关闭旧对话框，创建新的
                    self.compress_dialog.close()
                    self.compress_dialog.deleteLater()
            
            # 显示状态消息，提示用户正在处理
            self.show_status_message("正在准备压缩对话框...")
            
            # 创建新对话框 - 恢复同步创建方式以确保截图功能正常工作
            self.compress_dialog = CompressDialog(self)
            
            # 设置为非模态对话框
            self.compress_dialog.setWindowModality(Qt.NonModal)
            
            # 设置默认输出文件名（使用文件夹名称）
            folder_name = os.path.basename(os.path.normpath(self.current_folder))
            default_output = os.path.join(os.path.dirname(self.current_folder), f"{folder_name}.rar")
            
            # 确保对话框完全初始化后再设置输出路径
            QApplication.processEvents()
            
            if hasattr(self.compress_dialog, 'output_path') and self.compress_dialog.output_path is not None:
                self.compress_dialog.output_path.setText(default_output)
            
            # 显示对话框
            self.compress_dialog.show()
            
            # 使用定时器延迟加载文件列表，避免界面卡顿但保持对话框可见
            QTimer.singleShot(200, lambda: self.compress_dialog.update_files_list(self.current_folder))
            
            # 视频区域功能已移除
            
        except Exception as e:
            print(f"Error creating compress dialog: {str(e)}")
            QMessageBox.warning(self, "错误", f"创建压缩对话框时出错：{str(e)}")

    def show_status_message(self, message, timeout=3000):
        """在状态栏显示消息，3秒后自动消失"""
        self.statusBar().showMessage(message, timeout)

    def eventFilter(self, obj, event):
        """处理全局事件"""
        if obj == self:
            # 窗口激活事件
            if event.type() == QEvent.WindowActivate:
                pass
                
            # 窗口变化事件
            elif event.type() == QEvent.WindowStateChange:
                pass
                
        return super().eventFilter(obj, event)

    def on_splitter_moved(self, pos, index):
        """处理分隔器移动事件"""
        # 保存新的分隔位置
        settings = QSettings("2048Forum", "FolderViewer")
        settings.setValue("splitter_state", self.splitter.saveState())
        
        # 强制更新界面
        for i in range(self.splitter.count()):
            widget = self.splitter.widget(i)
            if widget:
                widget.updateGeometry()
                widget.update()
        
        # 特别处理文件夹列表
        if hasattr(self, 'drop_area') and self.drop_area:
            if hasattr(self.drop_area, 'folders_tree'):
                self.drop_area.folders_tree.updateGeometry()
                self.drop_area.folders_tree.viewport().update()
                self.drop_area.folders_tree.repaint()
        
        # 更新整个界面
        self.updateGeometry()
        self.update()
        QApplication.processEvents()

    def handle_folder_drop(self, path):
        """统一处理文件夹拖放的方法"""
        try:
            if not os.path.isdir(path):
                self.show_status_message(f"无效的文件夹路径: {path}")
                return False
                
            # 保存当前状态
            old_folder = self.current_folder
            
            # 打开文件夹
            success = self.open_folder(path)
            if not success:
                self.show_status_message(f"无法打开文件夹: {path}")
                return False
                
            # 如果压缩对话框已打开，更新其文件列表
            if hasattr(self, 'compress_dialog') and self.compress_dialog and self.compress_dialog.isVisible():
                try:
                    # 先清空当前文件夹，强制刷新
                    old_current_folder = self.compress_dialog.current_folder
                    self.compress_dialog.current_folder = None
                    
                    # 设置新文件夹路径
                    self.compress_dialog.current_folder = path
                    
                    # 更新输出路径
                    folder_name = os.path.basename(path)
                    default_output = os.path.join(os.path.dirname(path), f"{folder_name}.rar")
                    self.compress_dialog.output_path.setText(default_output)
                    
                    # 更新文件列表
                    self.compress_dialog.update_files_list(path)
                    
                    # 视频区域功能已移除
                    QApplication.processEvents()
                    
                    # 如果上一个文件夹有视频，但当前文件夹没有视频，或者反之，额外再次刷新
                    has_videos_old = False
                    has_videos_new = False
                    
                    # 检查上一个文件夹是否有视频
                    if old_current_folder and os.path.isdir(old_current_folder):
                        for root, _, files in os.walk(old_current_folder):
                            for file in files:
                                if is_video_file(os.path.join(root, file)):
                                    has_videos_old = True
                                    break
                            if has_videos_old:
                                break
                    
                    # 检查当前文件夹是否有视频
                    if path and os.path.isdir(path):
                        for root, _, files in os.walk(path):
                            for file in files:
                                if is_video_file(os.path.join(root, file)):
                                    has_videos_new = True
                                    break
                            if has_videos_new:
                                break
                    
                    # 视频状态变化处理已移除
                    if has_videos_old != has_videos_new:
                        QTimer.singleShot(300, lambda: self.compress_dialog.repaint())
                        QTimer.singleShot(400, lambda: self.compress_dialog.adjustSize())
                except Exception as e:
                    print(f"更新压缩对话框出错: {str(e)}")
            
            # 强制更新布局
            self.explorer_layout.update()
            self.central_widget.updateGeometry()
            self.update()
            QApplication.processEvents()
            
            self.show_status_message(f"已打开文件夹: {os.path.basename(path)}")
            return True
        
        except Exception as e:
            print(f"处理文件夹拖放出错: {str(e)}")
            self.show_status_message(f"处理文件夹拖放出错: {str(e)}")
            return False

    def resizeEvent(self, event):
        """处理窗口大小变化事件，重新调整按钮布局"""
        super().resizeEvent(event)
        
        # 如果按钮已经初始化
        if hasattr(self, 'pack_and_screenshot_button') and hasattr(self, 'cancel_button') and hasattr(self, 'ok_button'):
            button_layout = self.findChild(QGridLayout, "button_layout")
            if button_layout:
                # 清除现有布局中的按钮
                while button_layout.count():
                    item = button_layout.takeAt(0)
                    if item.widget():
                        item.widget().setParent(None)
                
                # 根据窗口宽度重新排列按钮
                if self.width() >= 800:
                    # 宽度足够时，一行放置三个按钮
                    button_layout.addWidget(self.pack_and_screenshot_button, 0, 0)
                    button_layout.addWidget(self.cancel_button, 0, 1)
                    button_layout.addWidget(self.ok_button, 0, 2)
                else:
                    # 宽度不足时，两行排列
                    button_layout.addWidget(self.pack_and_screenshot_button, 0, 0, 1, 2)
                    button_layout.addWidget(self.cancel_button, 1, 0)
                    button_layout.addWidget(self.ok_button, 1, 1)

def get_video_thumbnail(video_path, size=16):
    """获取视频缩略图
    Args:
        video_path: 视频文件路径
        size: 缩略图大小
    Returns:
        QIcon: 视频缩略图图标，如果失败则返回None
    """
    try:
        # 获取程序所在目录
        app_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        ffmpeg_path = os.path.join(app_dir, 'ffmpeg.exe')
        
        if not os.path.exists(ffmpeg_path):
            return None
            
        # 直接使用ffmpeg将缩略图输出到内存
        cmd = [
            ffmpeg_path,
            '-i', video_path,
            '-vframes', '1',  # 只提取一帧
            '-an',  # 不提取音频
            '-s', f'{size}x{size}',  # 设置大小
            '-ss', '0',  # 从开始提取
            '-f', 'image2pipe',  # 输出到管道
            '-vcodec', 'png',  # 使用PNG格式
            '-'  # 输出到stdout
        ]
        
        # 执行ffmpeg命令
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        
        # 获取输出
        stdout, stderr = process.communicate()
        
        # 检查是否成功生成缩略图
        if process.returncode == 0 and stdout:
            # 从内存数据创建图像
            image = QImage.fromData(stdout)
            if not image.isNull():
                # 缩放图像
                scaled_image = image.scaled(size, size, 
                                         Qt.KeepAspectRatio, 
                                         Qt.SmoothTransformation)
                return QIcon(QPixmap.fromImage(scaled_image))
    
    except Exception as e:
        print(f"获取视频缩略图失败: {str(e)}")
    
    return None

if __name__ == '__main__':
    try:
        # 1. 初始化Qt
        app = initialize_qt()
        logger.info("Qt环境初始化完成")
        
        # 2. 检查单实例
        sock = None
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.bind(('localhost', 45654))
        except socket.error:
            QMessageBox.warning(None, "提示", "程序已在运行中")
            sys.exit(0)
        
        # 3. 初始化COM
        pythoncom.CoInitialize()
        COM_INITIALIZED = True
        logger.info("COM环境初始化成功")
        
        # 4. 创建主窗口（使用事件循环确保UI响应）
        main_window = None
        
        def create_window():
            global main_window
            main_window = SimpleFolderViewer()
            main_window.show()
        
        # 使用定时器在事件循环中创建窗口
        QTimer.singleShot(0, create_window)
        
        # 5. 运行应用程序
        logger.info("进入应用程序主循环")
        exit_code = app.exec_()
        logger.info(f"应用程序退出，退出代码: {exit_code}")
        
    except Exception as e:
        logger.critical(f"程序启动过程中的未捕获异常: {str(e)}", exc_info=True)
        print(f"严重错误: {str(e)}")
        exit_code = 1
        
    finally:
        # 清理资源
        try:
            if main_window and hasattr(main_window, 'explorer'):
                main_window.explorer.deleteLater()
            if COM_INITIALIZED:
                pythoncom.CoUninitialize()
            if sock:
                sock.close()
        except:
            pass
        
        # 强制退出
        os._exit(exit_code) 